#ifndef __Usart1_H
#define __Usart1_H

#include "Hardware.h"


void usart1_initializes(void);
void usart_DMA_Config_TX(DMA_Channel_TypeDef *DMA_CHx, uint32_t cpar, uint32_t cmar, uint16_t cndtr);
void usart_DMA_Config_RX(DMA_Channel_TypeDef *DMA_CHx, uint32_t cpar, uint32_t cmar, uint16_t cndtr);
void usart1_send_data(uint8_t *data, uint8_t len);
void DMA_Enable(DMA_Channel_TypeDef *DMA_CHx, uint16_t CNDTR);


#endif
