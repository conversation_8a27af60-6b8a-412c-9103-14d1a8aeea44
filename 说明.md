### 按照以下要求优化本项目：

##### 项目介绍：

本项目基于APM32F091RCT6开发，实现四种不同气体浓度读取盒和四路膨胀力传感器数据读取。

##### 改进要求

1. Start、Library目录不允许修改。

2. 各个GPIO口配置：
   
   1. USART配置（根据项目中已有的USART配置方式，进一步根据以下要求进行修改或增加）：
   
      1. USART1（膨胀力传感器3）：TX连接PB6、RX连接PB7；查询指令：01 03 03 00 00 02 C4 4F；返回指令：01 03 04 00 00 03 A7 BB 79；返回值里的00 00 03 A7就是第一路的long型数值，转换成十进制就是 935
      2. USART2（CO传感器）：TX连接PA2、RX连接PA3；查询指令：11 01 01 ED；传感器应答：16 05 01 DF1-DF2 DF3-DF4 [CS]；CO浓度值=（DF1×256+DF2)/5；
      3. USART3（H2传感器）：TX连接PC4、RX连接PC5；查询指令：FF 01 86 00 00 00 00 00 79；传感器应答：FF 86 00（高位） D1（低位） 05（气体代码） 01（小数位数） 00 00 A3（接收指令以 O2 模组浓度值为 20.9%VOL 为例）；气体浓度值=气体浓度高位×256+气体浓度低位）×分辨率；该传感器上电默认主动发送模式，可以不发送查询指令，只对返回指令进行处理，甚至可以不配置TX引脚
      4. USART4（膨胀力传感器1）：TX连接PC10、RX连接PC11；与膨胀力传感器3完全相同
      5. USART5（膨胀力传感器2）：TX连接PB3、RX连接PB4；与膨胀力传感器3完全相同
      6. USART6（膨胀力传感器4）：TX连接PC0、RX连接PC1；与膨胀力传感器3完全相同
      7. USART7（O2传感器）：TX连接PC6、RX连接PC7；与H2传感器完全相同
      8. USART8（CO2传感器）：TX连接PC2、RX连接PC3；读浓度指令：FF 03 60 01 00 02 9E 15；返回数据：FF 03 04 00（高位） 00（低位） 77 B5 03 BB；读小数点个数指令：FF 03 20 31 00 01 CB DB；返回数据：FF 03 02 00（高位） 01（低位） 50 50；小数点个数为1，浓度要除以10
   
   2. CAN配置：
   
      CAN TX连接PA12、CAN RX连接PA11；将八个USART的数据打包成多次CAN报文输出，每个报文包括8字节数据，确保每批发送的数据排列为：CO浓度、H2浓度、O2浓度、CO2浓度、膨胀力1、膨胀力2、膨胀力3、膨胀力4；确保取的标识符合适
   
   3. GPIO配置（LED灯配置，LED高电平熄灭低电平点亮，每当对应的设备完成一次收发数据（或者CAN发送一次数据时），则对应的LED灯闪烁一次）；
   
      1. LED1（膨胀力传感器3）：PB9
      2. LED2（CO传感器）：PA4
      3. LED3（H2传感器）：PB0
      4. LED4（膨胀力传感器1）：PA15
      5. LED5（膨胀力传感器2）：PD2
      6. LED6（膨胀力传感器4）：PC15-OSC32_OUT
      7. LED7（O2传感器）：PC8
      8. LED8（CO2传感器）：PA0
      9. LED CAN：PB12
   
##### 可能需要的其它资料

1. APM32F091RCT6资料：
   1. 中断处理：
      1. USART1：独立中断处理
      2. USART2：独立中断处理
      3. USART3-8：共享中断
   2. 内核：
      1. 32 位 Arm® Cortex®-M0+内核
      2. 最高 48MHz 工作频率
   3. DMA：
      1. 两个 DMA，共 12 个通道，DMA1 有 7 个通道，DMA2 有 5 个通道
   4. 定时器：
      1. 1 个可以提供 7 通道 PWM 输出的 16 位高级定时器 TMR1，支持死区生成和刹车输入等功能
      2. 1 个 32 位通用定时器 TMR2，5 个 16 位通用定时器 TMR3/14/15/16/17，每个定时器最多有 4 个独立通道可以用来输入捕获、输出比较、PWM 与脉冲计数等功能
      3. 2 个 16 位基本定时器 TMR6/7
      4. 2 个看门狗定时器：一个独立看门狗 IWDT 和一个窗口看门狗 WWDT
      5. 1 个 24 位自减型系统滴答定时器 Sys Tick Timer