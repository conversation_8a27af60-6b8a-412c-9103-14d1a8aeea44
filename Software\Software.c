#include "Software.h"

// 定义所有USART管理结构体
usart_management_t usart1_management;  // 膨胀力传感器3
usart_management_t usart2_management;  // CO传感器
usart_management_t usart3_management;  // H2传感器
usart_management_t usart4_management;  // 膨胀力传感器1
usart_management_t usart5_management;  // 膨胀力传感器2
usart_management_t usart6_management;  // 膨胀力传感器4
usart_management_t usart7_management;  // O2传感器
usart_management_t usart8_management;  // CO2传感器

// 定义数据管理结构体
data_management_t data_management;

// 定义定时器管理结构体
timer2_management_t timer2_management;

void software_initializes(void)
{
    // 初始化所有USART管理结构体
    memset(&usart1_management, 0, sizeof(usart1_management));
    memset(&usart2_management, 0, sizeof(usart2_management));
    memset(&usart3_management, 0, sizeof(usart3_management));
    memset(&usart4_management, 0, sizeof(usart4_management));
    memset(&usart5_management, 0, sizeof(usart5_management));
    memset(&usart6_management, 0, sizeof(usart6_management));
    memset(&usart7_management, 0, sizeof(usart7_management));
    memset(&usart8_management, 0, sizeof(usart8_management));

    // 初始化数据管理结构体
    memset(&data_management, 0, sizeof(data_management));

    // 初始化定时器管理结构体
    memset(&timer2_management, 0, sizeof(timer2_management));
}
