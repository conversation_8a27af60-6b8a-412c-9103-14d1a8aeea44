#include "Usart7.h"

usart_management_t usart7_management;

/******************************************************************
 * @brief  初始化串口7，用于O2传感器通信
 *         TX连接PC6、RX连接PC7
 *         与H2传感器完全相同，上电默认主动发送模式
 * @input  无
 * @return 无
******************************************************************/
void usart7_initializes(void)
{
    USART_InitTypeDef USART_InitStructure;
    GPIO_InitTypeDef  GPIO_InitStructure;
    NVIC_InitTypeDef  NVIC_InitStructure;
    
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART7, ENABLE);
    RCC_AHBPeriphClockCmd(RCC_AHBPeriph_GPIOC, ENABLE);

    // 配置GPIO复用功能
    GPIO_PinAFConfig(GPIOC, GPIO_PinSource6, GPIO_AF_2);  // TX
    GPIO_PinAFConfig(GPIOC, GPIO_PinSource7, GPIO_AF_2);  // RX

    // 配置GPIO引脚
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_6 | GPIO_Pin_7;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(GPIOC, &GPIO_InitStructure);

    // 配置USART参数
    USART_OverSampling8Cmd(USART7, ENABLE);
    USART_InitStructure.USART_BaudRate = 9600;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
    USART_Init(USART7, &USART_InitStructure);
    
    // 配置中断 - USART3-8共享中断
    NVIC_InitStructure.NVIC_IRQChannel = USART3_8_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    // 使能接收中断
    USART_ITConfig(USART7, USART_IT_RXNE, ENABLE);
    USART_Cmd(USART7, ENABLE);
    USART_ClearFlag(USART7, USART_FLAG_TC);
}

/******************************************************************
 * @brief  通过DMA发送数据
 * @input  data：数据
 *         len：数据长度
 * @return 无
******************************************************************/
void usart7_send_data(uint8_t *data, uint8_t len)
{
    uint8_t i;
    for(i = 0; i < len; i++)
    {
        while(USART_GetFlagStatus(USART7, USART_FLAG_TXE) == RESET);
        USART_SendData(USART7, data[i]);
    }
    while(USART_GetFlagStatus(USART7, USART_FLAG_TC) == RESET);
}
