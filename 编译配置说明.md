# 编译配置说明

## 开发环境
- IDE: Keil MDK-ARM 5.x 或更高版本
- 编译器: ARM Compiler 5 或 ARM Compiler 6
- 目标芯片: APM32F091RCT6

## 项目配置

### 编译器设置
1. **C/C++选项卡**
   - Optimization Level: -O1 或 -O2
   - Language C: C99
   - Warnings: All Warnings

2. **预处理器定义**
   ```
   USE_STDPERIPH_DRIVER
   APM32F091
   ```

3. **包含路径**
   ```
   .\Hardware
   .\Software
   .\User
   .\Start
   .\Library
   ```

### 链接器设置
1. **Memory Layout**
   - ROM: 0x08000000, Size: 0x40000 (256KB)
   - RAM: 0x20000000, Size: 0x8000 (32KB)

2. **Stack Size**: 0x400 (1KB)
3. **Heap Size**: 0x200 (512B)

## 文件编码设置

### Keil MDK编码配置
1. 打开 Options for Target
2. 选择 C/C++ 选项卡
3. 在 Misc Controls 中添加: `--locale=chinese`
4. 确保所有源文件保存为GB2312编码

### 文件编码检查
确保以下文件使用GB2312编码:
- 所有.c和.h文件
- 项目配置文件
- 说明文档

## 编译顺序

### 依赖关系
```
Library (标准库)
    ↓
Start (启动文件)
    ↓
Hardware (硬件驱动层)
    ↓
Software (软件应用层)
    ↓
User (用户应用层)
```

### 编译步骤
1. 清理项目: Project → Clean
2. 重新编译: Project → Rebuild All Target Files
3. 检查编译输出，确保无错误和警告

## 常见编译问题及解决方案

### 1. 头文件找不到
**错误信息**: `fatal error: 'xxx.h' file not found`

**解决方案**:
- 检查包含路径设置
- 确认头文件存在且路径正确
- 检查文件名大小写

### 2. 函数未定义
**错误信息**: `undefined reference to 'xxx'`

**解决方案**:
- 检查函数声明和定义是否匹配
- 确认相关.c文件已添加到项目中
- 检查函数名拼写

### 3. 重复定义
**错误信息**: `multiple definition of 'xxx'`

**解决方案**:
- 检查是否在头文件中定义了变量
- 使用extern声明全局变量
- 检查是否重复包含了同一个.c文件

### 4. 内存不足
**错误信息**: `region 'ROM' overflowed`

**解决方案**:
- 优化代码，减少ROM使用
- 调整优化级别
- 检查是否有未使用的代码

### 5. 栈溢出
**错误信息**: `region 'RAM' overflowed`

**解决方案**:
- 增加栈大小设置
- 减少局部变量使用
- 优化递归函数

## 调试配置

### 调试器设置
1. **Debug选项卡**
   - Use: ST-Link Debugger 或 J-Link
   - Settings: 配置对应的调试器参数

2. **Flash Download**
   - Programming Algorithm: 选择对应的Flash算法
   - RAM for Algorithm: 0x20000000, Size: 0x1000

### 调试技巧
1. 使用断点调试关键函数
2. 观察变量窗口监控数据变化
3. 使用逻辑分析仪观察GPIO状态
4. 使用示波器检查信号时序

## 版本控制

### Git配置
```gitignore
# Keil项目文件
*.uvguix.*
*.uvoptx
Objects/
Listings/
DebugConfig/
*.scvd
```

### 提交规范
- 提交前确保编译通过
- 提交信息使用中文描述
- 重要修改需要添加详细说明

## 发布配置

### Release版本设置
1. Optimization Level: -O2
2. 关闭调试信息生成
3. 启用所有优化选项
4. 生成.hex和.bin文件

### 版本信息
在main.c中添加版本信息:
```c
const char* firmware_version = "V1.0.0";
const char* build_date = __DATE__;
const char* build_time = __TIME__;
```

## 质量保证

### 编译检查清单
- [ ] 无编译错误
- [ ] 无编译警告
- [ ] 代码大小在限制范围内
- [ ] 所有文件使用GB2312编码
- [ ] 中文注释正确显示
- [ ] 版本信息正确

### 代码审查要点
- 函数命名规范
- 注释完整性
- 错误处理机制
- 资源释放
- 中断安全性
