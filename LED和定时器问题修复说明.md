# LED和定时器问题修复说明

## 发现的关键问题

### 1. led_flash函数缺少延时
**原始问题**:
```c
void led_flash(led_num_t led)
{
    led_on(led);
    // 没有延时！
    led_off(led);
}
```

**修复方案**:
```c
void led_flash(led_num_t led)
{
    led_on(led);
    delay_ms(50);  // 添加50ms延时，让LED可见
    led_off(led);
}
```

### 2. 缺少delay函数头文件
在Led.c中添加了`#include "Delay.h"`

### 3. GPIOD时钟问题
将LED5从GPIOD改为GPIOB，避免可能的GPIOD时钟配置问题

## 当前测试配置

### 简化的测试程序
为了专注于基本功能测试，当前程序配置为：

1. **启动测试**: 所有LED闪烁5次（每次1秒周期）
2. **100ms测试**: LED1每100ms切换状态
3. **500ms测试**: LED_CAN每500ms切换状态  
4. **1秒测试**: LED2每秒闪烁一次

### 预期现象
如果系统正常工作，应该看到：
- **启动阶段**: 所有LED同时闪烁5次
- **运行阶段**: 
  - LED1快速闪烁（100ms周期）
  - LED_CAN中速闪烁（500ms周期）
  - LED2慢速闪烁（1秒闪一次，持续50ms）

## 进一步排查步骤

### 如果LED仍然不闪烁

#### 1. 检查GPIO基本功能
在main函数开始添加最简单的测试：
```c
// 强制控制一个LED
GPIO_ResetBits(GPIOB, GPIO_Pin_12); // 点亮LED_CAN
delay_ms(2000);                     // 等待2秒
GPIO_SetBits(GPIOB, GPIO_Pin_12);   // 熄灭LED_CAN
delay_ms(2000);                     // 等待2秒
```

#### 2. 检查定时器中断
在TIM2_IRQHandler中添加GPIO切换：
```c
void TIM2_IRQHandler(void)
{
    if(TIM_GetITStatus(TIM2, TIM_IT_Update) == SET)
    {
        TIM_ClearITPendingBit(TIM2, TIM_IT_Update);
        
        // 直接在中断中切换LED，验证中断是否触发
        GPIO_ToggleBits(GPIOB, GPIO_Pin_12);
        
        // 原有的定时器逻辑...
    }
}
```

#### 3. 检查系统时钟
```c
// 在main函数开始添加
RCC_ClocksTypeDef clocks;
RCC_GetClocksFreq(&clocks);
// 在调试器中查看clocks结构体的值
// 特别关注PCLK1_Frequency（APB1时钟）
```

### 可能的硬件问题

#### 1. LED连接问题
- 检查LED极性（阳极接电源，阴极接GPIO）
- 确认限流电阻存在
- 用万用表测量GPIO电压

#### 2. 电源问题
- 检查3.3V电源是否正常
- 确认GPIO电源域正常

#### 3. 晶振问题
- 检查外部晶振是否起振
- 确认时钟配置正确

## 调试工具使用

### 1. Keil调试器
- 在led_on/led_off函数中设置断点
- 观察GPIO寄存器状态
- 检查函数是否被调用

### 2. 示波器/逻辑分析仪
- 监测GPIO引脚电平变化
- 验证定时器中断频率

### 3. 万用表
- 测量GPIO输出电压
- 检查LED两端电压

## 系统时钟配置检查

### APM32F091典型时钟配置
- HSI: 8MHz（内部RC振荡器）
- PLL: 48MHz（系统时钟）
- AHB: 48MHz
- APB1: 48MHz（定时器时钟）

### 定时器计算验证
当前配置：
- Prescaler = 4799 (4800-1)
- Period = 999 (1000-1)
- 中断频率 = 48MHz / (4800 × 1000) = 10Hz = 100ms

## 下一步调试建议

### 优先级排序
1. **最高优先级**: 验证GPIO基本功能
2. **高优先级**: 验证定时器中断是否触发
3. **中优先级**: 检查系统时钟配置
4. **低优先级**: 检查硬件连接

### 逐步测试方法
1. 先测试一个LED的基本on/off功能
2. 再测试定时器中断
3. 最后测试完整的LED闪烁逻辑

### 如果基本功能都正常
如果GPIO和定时器都正常工作，但LED仍然不闪烁，可能是：
- LED硬件连接问题
- 电流驱动能力不足
- GPIO配置参数错误

## 临时解决方案

如果问题复杂，可以先使用轮询方式测试LED：
```c
while(1)
{
    led_on(LED1);
    delay_ms(500);
    led_off(LED1);
    delay_ms(500);
}
```

这样可以排除定时器相关的问题，专注于LED硬件功能验证。
