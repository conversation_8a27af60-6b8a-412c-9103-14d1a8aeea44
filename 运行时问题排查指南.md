# 运行时问题排查指南

## 问题现象
- 编译成功，但CAN报文没有发送
- LED一个都没有闪烁

## 已修复的问题

### 1. 中断处理函数位置错误
**问题**: TIM2_IRQHandler在Timer.c中定义，但没有在stm32f0xx_it.h中声明
**修复**: 
- 将TIM2_IRQHandler移动到stm32f0xx_it.c中
- 在stm32f0xx_it.h中添加函数声明
- 添加了所有中断处理函数的声明

### 2. 添加了测试功能
**启动LED测试**: 
- 系统启动时所有LED闪烁3次，验证LED硬件功能
- 添加心跳LED（LED_CAN每100ms闪烁），验证定时器功能

**CAN测试数据**:
- 添加了测试数据生成，确保即使没有传感器也能发送CAN数据
- 测试数据会递增，便于观察CAN通信是否正常

## 测试步骤

### 第一步：LED功能测试
1. **启动测试**: 上电后观察所有LED是否闪烁3次
   - 如果LED闪烁 → LED硬件和GPIO配置正常
   - 如果LED不闪烁 → 检查LED连接和GPIO配置

2. **心跳测试**: 观察LED_CAN是否每100ms闪烁一次
   - 如果闪烁 → 定时器和中断系统正常
   - 如果不闪烁 → 定时器或中断有问题

### 第二步：CAN通信测试
1. **连接CAN分析仪**到PA11(RX)和PA12(TX)
2. **设置CAN参数**: 500kbps波特率
3. **观察CAN数据**: 应该每秒收到3帧数据
   - 帧1 (ID: 0x201): 气体浓度数据
   - 帧2 (ID: 0x202): 膨胀力1、2数据  
   - 帧3 (ID: 0x203): 膨胀力3、4数据

### 第三步：传感器通信测试
1. **连接传感器**到对应USART
2. **观察对应LED**是否在数据收发时闪烁
3. **检查CAN数据**是否包含真实传感器数据

## 可能的问题和解决方案

### 1. LED不闪烁
**可能原因**:
- LED连接错误（极性反接）
- GPIO配置错误
- 电源问题

**检查方法**:
```c
// 在main函数开始添加简单测试
GPIO_ResetBits(GPIOB, GPIO_Pin_12); // 强制点亮LED_CAN
delay_ms(1000);
GPIO_SetBits(GPIOB, GPIO_Pin_12);   // 强制熄灭LED_CAN
```

### 2. 定时器不工作
**可能原因**:
- 时钟配置错误
- 中断优先级冲突
- 中断向量表错误

**检查方法**:
- 在TIM2_IRQHandler中添加断点
- 检查TIM2时钟是否使能
- 验证中断是否正确触发

### 3. CAN通信失败
**可能原因**:
- CAN收发器未连接
- 总线终端电阻缺失
- CAN时钟配置错误

**检查方法**:
- 使用示波器检查CAN_TX信号
- 检查CAN收发器电源
- 验证CAN总线电平

## 调试代码示例

### LED测试代码
```c
// 在main函数中添加
void test_all_leds(void)
{
    uint8_t i, j;
    for(i = 0; i < LED_MAX; i++)
    {
        led_on(i);
        delay_ms(100);
        led_off(i);
        delay_ms(100);
    }
}
```

### 定时器测试代码
```c
// 在TIM2_IRQHandler中添加
static uint32_t timer_counter = 0;
timer_counter++;
if(timer_counter % 1000 == 0) // 每100秒
{
    // 添加断点或LED指示
    led_toggle(LED1);
}
```

### CAN测试代码
```c
// 发送固定测试数据
uint8_t test_data[8] = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08};
CAN_SendData(0x123, test_data, 8);
```

## 系统时钟检查

### 时钟配置验证
确保以下时钟正确配置：
- 系统时钟：48MHz
- APB1时钟：用于TIM2、CAN、USART2-8
- APB2时钟：用于USART1、USART6-8
- AHB时钟：用于GPIO

### 时钟测试方法
```c
// 获取时钟频率
RCC_ClocksTypeDef clocks;
RCC_GetClocksFreq(&clocks);
// 在调试器中查看clocks结构体的值
```

## 下一步调试建议

1. **逐步测试**: 先确保基本功能（LED、定时器）正常
2. **单独测试**: 分别测试每个模块的功能
3. **硬件检查**: 确认硬件连接正确
4. **示波器验证**: 使用示波器检查关键信号
5. **CAN分析仪**: 使用专业工具验证CAN通信

## 常用调试工具

1. **Keil调试器**: 设置断点，观察变量
2. **逻辑分析仪**: 检查GPIO状态变化
3. **示波器**: 检查模拟信号质量
4. **CAN分析仪**: 验证CAN通信协议
5. **万用表**: 检查电源和连接

重新编译并下载程序后，按照以上步骤进行测试，应该能够定位问题所在。
