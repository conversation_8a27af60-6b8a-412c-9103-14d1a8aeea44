#ifndef __Led_H
#define __Led_H

#include "stm32f0xx.h"

// LED引脚定义
#define LED1_GPIO_PORT    GPIOB
#define LED1_GPIO_PIN     GPIO_Pin_9     // 膨胀力传感器3

#define LED2_GPIO_PORT    GPIOA
#define LED2_GPIO_PIN     GPIO_Pin_4     // CO传感器

#define LED3_GPIO_PORT    GPIOB
#define LED3_GPIO_PIN     GPIO_Pin_0     // H2传感器

#define LED4_GPIO_PORT    GPIOA
#define LED4_GPIO_PIN     GPIO_Pin_15    // 膨胀力传感器1

#define LED5_GPIO_PORT    GPIOB
#define LED5_GPIO_PIN     GPIO_Pin_1     // 膨胀力传感器2（改为GPIOB避免GPIOD问题）

#define LED6_GPIO_PORT    GPIOC
#define LED6_GPIO_PIN     GPIO_Pin_15    // 膨胀力传感器4

#define LED7_GPIO_PORT    GPIOC
#define LED7_GPIO_PIN     GPIO_Pin_8     // O2传感器

#define LED8_GPIO_PORT    GPIOA
#define LED8_GPIO_PIN     GPIO_Pin_0     // CO2传感器

#define LED_CAN_GPIO_PORT GPIOB
#define LED_CAN_GPIO_PIN  GPIO_Pin_12    // CAN通信

// LED编号定义
typedef enum
{
    LED1 = 0,  // 膨胀力传感器3
    LED2,      // CO传感器
    LED3,      // H2传感器
    LED4,      // 膨胀力传感器1
    LED5,      // 膨胀力传感器2
    LED6,      // 膨胀力传感器4
    LED7,      // O2传感器
    LED8,      // CO2传感器
    LED_CAN,   // CAN通信
    LED_MAX
} led_num_t;

// 函数声明
void led_initializes(void);
void led_on(led_num_t led);
void led_off(led_num_t led);
void led_toggle(led_num_t led);
void led_flash(led_num_t led);

#endif
