# 膨胀力气体采集板测试验证说明

## 测试环境准备

### 硬件连接
1. **传感器连接**
   - CO传感器连接到USART2 (PA2/PA3)
   - H2传感器连接到USART3 (PC4/PC5)
   - O2传感器连接到USART7 (PC6/PC7)
   - CO2传感器连接到USART8 (PC2/PC3)
   - 膨胀力传感器1连接到USART4 (PC10/PC11)
   - 膨胀力传感器2连接到USART5 (PB3/PB4)
   - 膨胀力传感器3连接到USART1 (PB6/PB7)
   - 膨胀力传感器4连接到USART6 (PC0/PC1)

2. **CAN总线连接**
   - CAN_TX: PA12
   - CAN_RX: PA11
   - 连接CAN分析仪或其他CAN设备

3. **LED指示灯**
   - 确保所有LED正确连接到对应GPIO引脚

### 软件工具
1. Keil MDK开发环境
2. CAN分析仪软件
3. 串口调试助手（可选）

## 功能测试

### 1. 基本功能测试

#### 1.1 系统启动测试
- 上电后检查所有LED是否正常（初始状态应为熄灭）
- 观察系统是否正常运行，无死机现象

#### 1.2 定时器功能测试
- 观察LED闪烁频率，验证定时器是否正常工作
- 500ms发送查询指令
- 1s发送CAN数据

### 2. 传感器通信测试

#### 2.1 膨胀力传感器测试
- 连接膨胀力传感器到对应USART
- 观察对应LED是否在接收到数据时闪烁
- 验证数据解析是否正确

#### 2.2 气体传感器测试
- 连接各气体传感器
- CO传感器: 验证查询指令发送和数据接收
- H2/O2传感器: 验证主动发送数据的接收
- CO2传感器: 验证查询指令和数据解析

### 3. CAN通信测试

#### 3.1 CAN数据发送测试
- 使用CAN分析仪监听CAN总线
- 验证每秒发送3帧数据:
  - 帧1 (ID: 0x201): CO、H2、O2、CO2浓度数据
  - 帧2 (ID: 0x202): 膨胀力1、膨胀力2数据
  - 帧3 (ID: 0x203): 膨胀力3、膨胀力4数据

#### 3.2 数据格式验证
- 验证数据排列顺序是否正确
- 验证数据转换是否正确（高低字节顺序）

### 4. LED指示测试

#### 4.1 传感器LED测试
- 每个传感器数据接收时，对应LED应闪烁一次
- 验证LED1-LED8分别对应正确的传感器

#### 4.2 CAN LED测试
- CAN数据发送时，LED_CAN应闪烁

## 测试用例

### 测试用例1: 膨胀力传感器数据接收
**测试步骤:**
1. 连接膨胀力传感器到USART1
2. 观察LED1闪烁
3. 通过CAN分析仪查看数据

**预期结果:**
- LED1每500ms闪烁一次（查询时）
- 接收到数据时LED1再次闪烁
- CAN数据中包含正确的膨胀力数值

### 测试用例2: CO传感器数据接收
**测试步骤:**
1. 连接CO传感器到USART2
2. 观察LED2闪烁
3. 验证浓度计算公式

**预期结果:**
- LED2正常闪烁
- CAN数据中CO浓度值正确

### 测试用例3: CAN数据完整性测试
**测试步骤:**
1. 连接所有传感器
2. 监听CAN总线1分钟
3. 分析接收到的数据

**预期结果:**
- 每秒接收到3帧CAN数据
- 数据格式正确
- 数据连续性良好

## 故障排除

### 常见问题及解决方案

1. **LED不闪烁**
   - 检查GPIO配置是否正确
   - 检查LED连接是否正确
   - 验证LED极性（低电平点亮）

2. **传感器无数据**
   - 检查USART配置是否正确
   - 检查波特率设置
   - 验证传感器连接

3. **CAN数据异常**
   - 检查CAN总线配置
   - 验证数据打包逻辑
   - 检查CAN终端电阻

4. **系统死机**
   - 检查中断优先级设置
   - 验证DMA通道分配
   - 检查栈溢出问题

## 性能指标

### 实时性要求
- 传感器查询周期: 500ms
- CAN数据发送周期: 1s
- 中断响应时间: <10μs

### 可靠性要求
- 连续运行时间: >24小时
- 数据丢失率: <0.1%
- 通信错误率: <0.01%

## 验收标准

1. 所有传感器数据正常接收和解析
2. CAN数据格式和时序正确
3. LED指示功能正常
4. 系统稳定运行无死机
5. 编码格式为GB2312，中文注释正确显示
