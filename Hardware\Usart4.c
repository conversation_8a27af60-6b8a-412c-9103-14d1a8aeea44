#include "Usart4.h"

usart_management_t usart4_management;

/******************************************************************
 * @brief  初始化串口4，用于膨胀力传感器1通信
 *         TX连接PC10、RX连接PC11
 * @input  无
 * @return 无
******************************************************************/
void usart4_initializes(void)
{
    USART_InitTypeDef USART_InitStructure;
    GPIO_InitTypeDef  GPIO_InitStructure;
    NVIC_InitTypeDef  NVIC_InitStructure;
    
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART4, ENABLE);
    RCC_AHBPeriphClockCmd(RCC_AHBPeriph_GPIOC, ENABLE);

    // 配置GPIO复用功能
    GPIO_PinAFConfig(GPIOC, GPIO_PinSource10, GPIO_AF_0);  // TX
    GPIO_PinAFConfig(GPIOC, GPIO_PinSource11, GPIO_AF_0);  // RX

    // 配置GPIO引脚
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10 | GPIO_Pin_11;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(GPIOC, &GPIO_InitStructure);

    // 配置USART参数
    USART_OverSampling8Cmd(USART4, ENABLE);
    USART_InitStructure.USART_BaudRate = 9600;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
    USART_Init(USART4, &USART_InitStructure);
    
    // 配置中断 - USART3-8共享中断
    NVIC_InitStructure.NVIC_IRQChannel = USART3_8_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    // 使能接收中断
    USART_ITConfig(USART4, USART_IT_IDLE, ENABLE);
    USART_DMACmd(USART4, USART_DMAReq_Rx | USART_DMAReq_Tx, ENABLE);
    USART_Cmd(USART4, ENABLE);
    USART_ClearFlag(USART4, USART_FLAG_TC);
    
    // 配置DMA - 使用不同的DMA通道
    usart_DMA_Config_TX(DMA2_Channel1, (uint32_t)&USART4->TDR, 
                       (uint32_t)usart4_management.sendBuffer, USART_SEND_BUFFER_SIZE);
    usart_DMA_Config_RX(DMA2_Channel2, (uint32_t)&USART4->RDR, 
                       (uint32_t)usart4_management.receiveBuffer, USART_RECEIVE_BUFFER_SIZE);
}

/******************************************************************
 * @brief  通过DMA发送数据
 * @input  data：数据
 *         len：数据长度
 * @return 无
******************************************************************/
void usart4_send_data(uint8_t *data, uint8_t len)
{
    DMA_ClearFlag(DMA2_FLAG_TC1);
    DMA_Cmd(DMA2_Channel1, DISABLE);
    memcpy(usart4_management.sendBuffer, data, len);
    DMA_Enable(DMA2_Channel1, len);
}
