#include "Hardware.h"

void hardware_initializes(void)
{
	GPIO_InitTypeDef  GPIO_InitStructure;
	
	delay_init();
	usart1_initializes();
	usart2_initializes();
	can_initializes();
	timer_initializes();
	
	RCC_AHBPeriphClockCmd(RCC_AHBPeriph_GPIOB, ENABLE);
	
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
	GPIO_Init(GPIOB, &GPIO_InitStructure);
	
	GPIO_SetBits(GPIOB, GPIO_Pin_10);
}

void LED_turn(void)
{
	if(GPIO_ReadInputDataBit(GPIOB, GPIO_Pin_10) == SET)
	{
		GPIO_ResetBits(GPIOB, GPIO_Pin_10);
	}
	else
	{
		GPIO_SetBits(GPIOB, GPIO_Pin_10);
	}
}
