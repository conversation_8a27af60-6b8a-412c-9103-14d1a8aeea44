#include "Hardware.h"

void hardware_initializes(void)
{
	// 初始化延时函数
	delay_init();

	// 初始化所有USART
	usart1_initializes();  // 膨胀力传感器3
	usart2_initializes();  // CO传感器
	usart3_initializes();  // H2传感器
	usart4_initializes();  // 膨胀力传感器1
	usart5_initializes();  // 膨胀力传感器2
	usart6_initializes();  // 膨胀力传感器4
	usart7_initializes();  // O2传感器
	usart8_initializes();  // CO2传感器

	// 初始化CAN通信
	can_initializes();

	// 初始化定时器
	timer_initializes();

	// 初始化LED
	led_initializes();
}

void LED_turn(void)
{
	// 保留原有的LED控制函数，用于兼容性
	if(GPIO_ReadInputDataBit(GPIOB, GPIO_Pin_10) == SET)
	{
		GPIO_ResetBits(GPIOB, GPIO_Pin_10);
	}
	else
	{
		GPIO_SetBits(GPIOB, GPIO_Pin_10);
	}
}
