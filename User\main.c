#include "Hardware.h"

uint8_t read_data_command[8] = {0xff, 0x03, 0x60, 0x01, 0x00, 0x01, 0xde, 0x14};

//uint16_t temp;

int main(void)
{	
	hardware_initializes();
	software_initializes();
	
	while(1)
	{
		if(timer2_management.flag_100ms)
		{
			timer2_management.flag_100ms = 0;
			
		}
		/*????CAN?????????100ms????????????????????????????*/
		if(timer2_management.flag_900ms)
		{
			timer2_management.flag_900ms = 0;
			
			/*???????????????????????crc��??????????????????????????*/
//			temp = Crc16(read_data_command, 6);
//			read_data_command[6] = temp;
//			read_data_command[7] = temp >> 8;
			
			usart2_send_data(read_data_command, 8);//????????????????????
		}
		/*?????????CAN????????*/
		if(timer2_management.flag_1s)
		{
			timer2_management.flag_1s = 0;
			
			CAN_SendData(0x20A, data_management.concentration, 8);
			LED_turn();
			
		}
	}
}



