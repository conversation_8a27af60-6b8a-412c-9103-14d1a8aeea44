#include "Hardware.h"

// 传感器查询指令定义
uint8_t co_query_cmd[4] = {0x11, 0x01, 0x01, 0xED};                           // CO传感器查询指令
uint8_t h2_query_cmd[9] = {0xFF, 0x01, 0x86, 0x00, 0x00, 0x00, 0x00, 0x00, 0x79}; // H2传感器查询指令
uint8_t expansion_query_cmd[8] = {0x01, 0x03, 0x03, 0x00, 0x00, 0x02, 0xC4, 0x4F}; // 膨胀力传感器查询指令
uint8_t co2_concentration_cmd[8] = {0xFF, 0x03, 0x60, 0x01, 0x00, 0x02, 0x9E, 0x15}; // CO2浓度查询指令
uint8_t co2_decimal_cmd[8] = {0xFF, 0x03, 0x20, 0x31, 0x00, 0x01, 0xCB, 0xDB};      // CO2小数点查询指令

void prepare_can_data(void);
void send_sensor_queries(void);

int main(void)
{
	hardware_initializes();
	software_initializes();

	while(1)
	{
		// 100ms定时处理
		if(timer2_management.flag_100ms)
		{
			timer2_management.flag_100ms = 0;
			// 可以在这里添加快速响应的处理
		}

		// 500ms定时发送传感器查询指令
		if(timer2_management.flag_500ms)
		{
			timer2_management.flag_500ms = 0;
			send_sensor_queries();
		}

		// 900ms定时处理（保留原有逻辑）
		if(timer2_management.flag_900ms)
		{
			timer2_management.flag_900ms = 0;
			// 可以在这里添加其他处理
		}

		// 每秒通过CAN发送数据
		if(timer2_management.flag_1s)
		{
			timer2_management.flag_1s = 0;

			// 准备CAN数据
			prepare_can_data();

			// 发送CAN数据（分多次发送，每次8字节）
			// 第一帧：CO浓度、H2浓度、O2浓度、CO2浓度
			CAN_SendData(0x201, data_management.can_data, 8);

			// 第二帧：膨胀力1、膨胀力2
			CAN_SendData(0x202, &data_management.can_data[8], 8);

			// 第三帧：膨胀力3、膨胀力4
			CAN_SendData(0x203, &data_management.can_data[16], 8);

			// CAN LED闪烁
			led_flash(LED_CAN);
		}
	}
}

/******************************************************************
 * @brief  准备CAN发送数据
 * @input  无
 * @return 无
******************************************************************/
void prepare_can_data(void)
{
	// 按照要求排列数据：CO浓度、H2浓度、O2浓度、CO2浓度、膨胀力1、膨胀力2、膨胀力3、膨胀力4

	// 第一帧数据：气体浓度数据
	data_management.can_data[0] = (uint8_t)(data_management.co_concentration >> 8);
	data_management.can_data[1] = (uint8_t)(data_management.co_concentration & 0xFF);
	data_management.can_data[2] = (uint8_t)(data_management.h2_concentration >> 8);
	data_management.can_data[3] = (uint8_t)(data_management.h2_concentration & 0xFF);
	data_management.can_data[4] = (uint8_t)(data_management.o2_concentration >> 8);
	data_management.can_data[5] = (uint8_t)(data_management.o2_concentration & 0xFF);
	data_management.can_data[6] = (uint8_t)(data_management.co2_concentration >> 8);
	data_management.can_data[7] = (uint8_t)(data_management.co2_concentration & 0xFF);

	// 第二帧数据：膨胀力1、膨胀力2
	data_management.can_data[8] = (uint8_t)(data_management.expansion_force1 >> 24);
	data_management.can_data[9] = (uint8_t)(data_management.expansion_force1 >> 16);
	data_management.can_data[10] = (uint8_t)(data_management.expansion_force1 >> 8);
	data_management.can_data[11] = (uint8_t)(data_management.expansion_force1 & 0xFF);
	data_management.can_data[12] = (uint8_t)(data_management.expansion_force2 >> 24);
	data_management.can_data[13] = (uint8_t)(data_management.expansion_force2 >> 16);
	data_management.can_data[14] = (uint8_t)(data_management.expansion_force2 >> 8);
	data_management.can_data[15] = (uint8_t)(data_management.expansion_force2 & 0xFF);

	// 第三帧数据：膨胀力3、膨胀力4
	data_management.can_data[16] = (uint8_t)(data_management.expansion_force3 >> 24);
	data_management.can_data[17] = (uint8_t)(data_management.expansion_force3 >> 16);
	data_management.can_data[18] = (uint8_t)(data_management.expansion_force3 >> 8);
	data_management.can_data[19] = (uint8_t)(data_management.expansion_force3 & 0xFF);
	data_management.can_data[20] = (uint8_t)(data_management.expansion_force4 >> 24);
	data_management.can_data[21] = (uint8_t)(data_management.expansion_force4 >> 16);
	data_management.can_data[22] = (uint8_t)(data_management.expansion_force4 >> 8);
	data_management.can_data[23] = (uint8_t)(data_management.expansion_force4 & 0xFF);
}

/******************************************************************
 * @brief  发送传感器查询指令
 * @input  无
 * @return 无
******************************************************************/
void send_sensor_queries(void)
{
	// 发送CO传感器查询指令
	usart2_send_data(co_query_cmd, sizeof(co_query_cmd));

	// H2和O2传感器上电默认主动发送，可以不发送查询指令
	// 如果需要查询，可以取消下面的注释
	// usart3_send_data(h2_query_cmd, sizeof(h2_query_cmd));
	// usart7_send_data(h2_query_cmd, sizeof(h2_query_cmd));

	// 发送膨胀力传感器查询指令
	usart1_send_data(expansion_query_cmd, sizeof(expansion_query_cmd)); // 膨胀力传感器3
	usart4_send_data(expansion_query_cmd, sizeof(expansion_query_cmd)); // 膨胀力传感器1
	usart5_send_data(expansion_query_cmd, sizeof(expansion_query_cmd)); // 膨胀力传感器2
	usart6_send_data(expansion_query_cmd, sizeof(expansion_query_cmd)); // 膨胀力传感器4

	// 发送CO2传感器查询指令
	usart8_send_data(co2_concentration_cmd, sizeof(co2_concentration_cmd));
}



