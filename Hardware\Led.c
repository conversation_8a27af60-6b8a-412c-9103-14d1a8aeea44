#include "Led.h"
#include "Delay.h"

// LED GPIO配置表
typedef struct
{
    GPIO_TypeDef* gpio_port;
    uint16_t gpio_pin;
} led_config_t;

static const led_config_t led_configs[LED_MAX] = 
{
    {LED1_GPIO_PORT, LED1_GPIO_PIN},    // LED1 - 膨胀力传感器3
    {LED2_GPIO_PORT, LED2_GPIO_PIN},    // LED2 - CO传感器
    {LED3_GPIO_PORT, LED3_GPIO_PIN},    // LED3 - H2传感器
    {LED4_GPIO_PORT, LED4_GPIO_PIN},    // LED4 - 膨胀力传感器1
    {LED5_GPIO_PORT, LED5_GPIO_PIN},    // LED5 - 膨胀力传感器2
    {LED6_GPIO_PORT, LED6_GPIO_PIN},    // LED6 - 膨胀力传感器4
    {LED7_GPIO_PORT, LED7_GPIO_PIN},    // LED7 - O2传感器
    {LED8_GPIO_PORT, LED8_GPIO_PIN},    // LED8 - CO2传感器
    {LED_CAN_GPIO_PORT, LED_CAN_GPIO_PIN} // LED_CAN - CAN通信
};

/******************************************************************
 * @brief  初始化所有LED GPIO
 * @input  无
 * @return 无
******************************************************************/
void led_initializes(void)
{
	  uint8_t i;
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 使能所有需要的GPIO时钟
    RCC_AHBPeriphClockCmd(RCC_AHBPeriph_GPIOA | RCC_AHBPeriph_GPIOB | 
                          RCC_AHBPeriph_GPIOC | RCC_AHBPeriph_GPIOD, ENABLE);
    
    // 配置GPIO参数
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    
    // 初始化所有LED GPIO
    for(i = 0; i < LED_MAX; i++)
    {
        GPIO_InitStructure.GPIO_Pin = led_configs[i].gpio_pin;
        GPIO_Init(led_configs[i].gpio_port, &GPIO_InitStructure);

        // 初始状态：LED熄灭（高电平）
        GPIO_SetBits(led_configs[i].gpio_port, led_configs[i].gpio_pin);
    }
}

/******************************************************************
 * @brief  点亮LED（低电平点亮）
 * @input  led: LED编号
 * @return 无
******************************************************************/
void led_on(led_num_t led)
{
    if(led < LED_MAX)
    {
        GPIO_ResetBits(led_configs[led].gpio_port, led_configs[led].gpio_pin);
    }
}

/******************************************************************
 * @brief  熄灭LED（高电平熄灭）
 * @input  led: LED编号
 * @return 无
******************************************************************/
void led_off(led_num_t led)
{
    if(led < LED_MAX)
    {
        GPIO_SetBits(led_configs[led].gpio_port, led_configs[led].gpio_pin);
    }
}

/******************************************************************
 * @brief  切换LED状态
 * @input  led: LED编号
 * @return 无
******************************************************************/
void led_toggle(led_num_t led)
{
    if(led < LED_MAX)
    {
        if(GPIO_ReadOutputDataBit(led_configs[led].gpio_port, led_configs[led].gpio_pin) == SET)
        {
            GPIO_ResetBits(led_configs[led].gpio_port, led_configs[led].gpio_pin);
        }
        else
        {
            GPIO_SetBits(led_configs[led].gpio_port, led_configs[led].gpio_pin);
        }
    }
}

/******************************************************************
 * @brief  LED闪烁一次（点亮后立即熄灭）
 * @input  led: LED编号
 * @return 无
******************************************************************/
void led_flash(led_num_t led)
{
    if(led < LED_MAX)
    {
        led_on(led);
        delay_ms(50);  // 添加50ms延时，让LED可见
        led_off(led);
    }
}
