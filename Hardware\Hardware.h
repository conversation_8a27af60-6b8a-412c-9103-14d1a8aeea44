#ifndef __Hardware_H
#define __Hardware_H

#include "stm32f0xx.h"
#include "string.h"

#include "Delay.h"
#include "Usart1.h"
#include "Usart2.h"
#include "Usart3.h"
#include "Usart4.h"
#include "Usart5.h"
#include "Usart6.h"
#include "Usart7.h"
#include "Usart8.h"
#include "Can.h"
#include "Timer.h"
#include "Led.h"

#include "Software.h"
#include "crc16.h"

void hardware_initializes(void);
void LED_turn(void);

#endif
