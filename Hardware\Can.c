#include "Can.h"

uint8_t buf1[6][8];
uint8_t j = 0;

/******************************************************************
 * @brief  ��ʼ��CAN��������500kbps
 * @input  ��
 * @return ��
******************************************************************/
void can_initializes(void)
{
	GPIO_InitTypeDef      GPIO_InitStructure;
    NVIC_InitTypeDef      NVIC_InitStructure;
    CAN_InitTypeDef       CAN_InitStructure;
    CAN_FilterInitTypeDef CAN_FilterInitStructure;
    
    RCC_AHBPeriphClockCmd(RCC_AHBPeriph_GPIOA, ENABLE);
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_CAN, ENABLE);
    
    GPIO_PinAFConfig(GPIOA, GPIO_PinSource11, GPIO_AF_4);
    GPIO_PinAFConfig(GPIOA, GPIO_PinSource12, GPIO_AF_4); 
    
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_11;//RX
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd  = GPIO_PuPd_NOPULL;
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_12;//TX
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd  = GPIO_PuPd_NOPULL;
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    
    NVIC_InitStructure.NVIC_IRQChannel = CEC_CAN_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
    
    CAN_DeInit(CAN);
    CAN_StructInit(&CAN_InitStructure);
    
    /*< ʱ�䴥��ͨ��ģʽ DISABLE:��ֹʱ�䴥��ͨ��ģʽ ENABLE:ʹ��ʱ�䴥��ͨ��ģʽ*/
    CAN_InitStructure.CAN_TTCM = DISABLE;
    
    /*< �Զ������߹رչ��� ��λ���� CAN Ӳ�����˳����߹ر�״̬ʱ����Ϊ
            DISABLE: ���������������һ����⵽ 128 ������ 11 ������λ��
            ���������� CAN_MCR �Ĵ����� INRQ λ���� 1 �����㣬���˳����߹ر�״̬
            ENABLE: һ����⵽ 128 ������ 11 ������λ����ͨ��Ӳ���Զ��˳����߹ر�״̬ */        
    CAN_InitStructure.CAN_ABOM = ENABLE;
    
    /*< �Զ�����ģʽ ��λ���� CAN Ӳ����˯��ģʽ�½��յ���Ϣʱ����Ϊ
            DISABLE: ������ͨ���� CAN_MCR �Ĵ����� SLEEP λ���㷢��������˳�˯��ģʽ
            ENABLE: һ����⵽ CAN ��Ϣ����ͨ��Ӳ���Զ��˳�˯��ģʽ */
    CAN_InitStructure.CAN_AWUM = ENABLE;
    
    /*< �Զ��ط���
            DISABLE: ���۷��ͽ����Σ��ɹ���������ٲö�ʧ������Ϣ��ֻ����һ��
            ENABLE: CAN Ӳ�����Զ��ط�����Ϣ��ֱ������ CAN ��׼��Ϣ���ͳɹ� */
    CAN_InitStructure.CAN_NART = ENABLE;//DISABLE;
    
    /*< ���� FIFO ����ģʽ
            DISABLE: ���� FIFO ��������������� FIFO װ������һ��������Ϣ������ǰһ����Ϣ
            ENABLE: ���� FIFO ��������������� FIFO װ������һ��������Ϣ��������*/
    CAN_InitStructure.CAN_RFLM = DISABLE;
    
    /*< ���� FIFO ���ȼ� 
            DISABLE: ���ȼ�����Ϣ��ʶ��ȷ�� 
            ENABLE: ���ȼ�������˳��ʱ��˳��ȷ��*/ 
    CAN_InitStructure.CAN_TXFP = DISABLE;
    
    /*< CAN����ģʽ CAN_Mode_Normal:����ģʽ CAN_Mode_LoopBack:�ػ�ģʽ 
            CAN_Mode_Silent:��Ĭģʽ CAN_Mode_Silent_LoopBack:��Ĭ�ػ�ģʽ*/
    CAN_InitStructure.CAN_Mode = CAN_Mode_Normal;
    
    //APB1ʱ��8M��Ԥ��Ƶ2��CANʱ��4M��500K
    CAN_InitStructure.CAN_Prescaler = 2;//����CAN�����ʷ�Ƶϵ��
    CAN_InitStructure.CAN_SJW = CAN_SJW_1tq;//����ͬ����Ծ����
    CAN_InitStructure.CAN_BS1 = CAN_BS1_3tq;//ʱ���1ռ��ʱ�䵥Ԫ
    CAN_InitStructure.CAN_BS2 = CAN_BS2_4tq;//ʱ���2ռ��ʱ�䵥Ԫ
    
    CAN_Init(CAN, &CAN_InitStructure);
    
    //ʹ��ɸѡ����0��32λ����ģʽ�����ɹ���һ��ID
    //ID�Ŵ����CAN_F0R1�У���CAN_FilterIdHigh��CAN_FilterIdLow�ֱ��ߵ�16λ
    //��������CAN_F0R2�У���CAN_FilterMaskIdHigh��CAN_FilterMaskIdLow�ֱ��ߵ�16λ
    //����ȫ��Ϊ0ʱ��������������κ�ID�����������ϵ����б���
    //��ʼ��ɸѡ��
    CAN_FilterInitStructure.CAN_FilterNumber = 0;//������0
    CAN_FilterInitStructure.CAN_FilterScale = CAN_FilterScale_32bit;//32λ�� 
    CAN_FilterInitStructure.CAN_FilterMode = CAN_FilterMode_IdMask;//����λ(����)ģʽ
    //32λ�����׼IDģʽ��ɸѡ��ֻɸѡ���ձ�׼IDΪ0x1�ı���
    CAN_FilterInitStructure.CAN_FilterIdHigh = 0x000 << 5;//(((uint32_t)0X01<<21|CAN_Id_Standard|CAN_RTR_Data)&0xFFFF0000)>>16;//ɸѡID
    CAN_FilterInitStructure.CAN_FilterIdLow = 0x0000;//((uint32_t)0X01<<21|CAN_Id_Standard|CAN_RTR_Data)&0x0000FFFF;//ɸѡID
    //(��Ҫ����Զ��֡ �޸Ķ�Ӧ����λΪ0)
    CAN_FilterInitStructure.CAN_FilterMaskIdHigh = 0x0000;//����
    CAN_FilterInitStructure.CAN_FilterMaskIdLow = 0x0000;//����
    CAN_FilterInitStructure.CAN_FilterFIFOAssignment = CAN_Filter_FIFO0;//ѡ�й�����FIFO0
    CAN_FilterInitStructure.CAN_FilterActivation = ENABLE;//���������0
    
    CAN_FilterInit(&CAN_FilterInitStructure);
    //��CAN_FilterInit�������У�
    /*�ڳ�ʼ��֮ǰ�������� CAN_FMR �Ĵ����� INRQ Ϊ INIT ��������ʼ��ģʽ��
    Ȼ���ʼ�� CAN �˲�����صļĴ���֮�󣬻����� CAN_FMR �Ĵ����� FINIT Ϊ 0 �����˳�
    ��ʼ��ģʽ��*/
    
    /* CAN FIFO0 �����ж�ʹ�� */ 
 //   CAN_ITConfig(CAN, CAN_IT_FMP0, ENABLE);
    
    CAN_ClearFlag(CAN,CAN_FLAG_RQCP0);
}

/******************************************************************
 * @brief  CAN��������
 * @input  ID������֡ID
 *         data������
 *         length�����ݳ���
 * @return ��
******************************************************************/
void CAN_SendData(uint16_t ID, uint8_t* data,uint8_t length)
{
    uint8_t mailbox;
    uint16_t i=0;
    CanTxMsg tx_msg;
    
    tx_msg.StdId=ID;             // ��׼��ʶ��
    tx_msg.IDE=CAN_Id_Standard;    //  ��׼֡
    tx_msg.RTR=CAN_RTR_Data;       // ����֡
    tx_msg.DLC = length;
    
	memcpy(tx_msg.Data,data,length);
        
	while ((CAN->TSR & CAN_TSR_TME0) == 0 && 
           (CAN->TSR & CAN_TSR_TME1) == 0 && 
           (CAN->TSR & CAN_TSR_TME2) == 0)
	{
		
	}
	
    mailbox = CAN_Transmit(CAN,&tx_msg);
    i=0;
    while (CAN_TransmitStatus(CAN, mailbox) == CAN_TxStatus_Failed && (i < 0xFFF))
    {
        i++;  // 添加递增，避免死循环
    }
}

/******************************************************************
 * @brief  CAN��������
 * @input  ��
 * @return ��
******************************************************************/
void CAN1_Receive(void)
{
	uint8_t i;
    CanRxMsg can_rx_msg;
    
    // ����Ƿ���CAN��Ϣ����
    if (CAN_MessagePending(CAN,CAN_FIFO0) > 0)
    {
        // ����CAN��Ϣ
        CAN_Receive(CAN,CAN_FIFO0,&can_rx_msg);
        /* �������յ���CAN��Ϣ */
        for(i=0;i<can_rx_msg.DLC;i++)
        {
            buf1[j][i]=can_rx_msg.Data[i];
        }
    }
}

void CEC_CAN_IRQHandler(void)
{
	if(CAN_GetITStatus(CAN, CAN_IT_FMP0) == SET)
	{
		CAN_ClearITPendingBit(CAN, CAN_IT_FMP0);
		if(++j == 6)j = 0;
		CAN1_Receive();
	}
}
