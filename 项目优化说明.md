# 膨胀力气体采集板项目优化说明

## 项目概述
本项目基于APM32F091RCT6微控制器开发，实现四种不同气体浓度读取和四路膨胀力传感器数据读取功能。

## 硬件配置

### USART配置
1. **USART1（膨胀力传感器3）**
   - TX: PB6, RX: PB7
   - 查询指令: 01 03 03 00 00 02 C4 4F
   - 返回指令: 01 03 04 00 00 03 A7 BB 79
   - 数据解析: 4字节long型数值

2. **USART2（CO传感器）**
   - TX: PA2, RX: PA3
   - 查询指令: 11 01 01 ED
   - 传感器应答: 16 05 01 DF1-DF2 DF3-DF4 [CS]
   - 浓度计算: (DF1×256+DF2)/5

3. **USART3（H2传感器）**
   - TX: PC4, RX: PC5
   - 查询指令: FF 01 86 00 00 00 00 00 79
   - 传感器应答: FF 86 00（高位） D1（低位） 05（气体代码） 01（小数位数） 00 00 A3
   - 浓度计算: (高位×256+低位)×分辨率
   - 注意: 上电默认主动发送模式

4. **USART4（膨胀力传感器1）**
   - TX: PC10, RX: PC11
   - 与膨胀力传感器3完全相同

5. **USART5（膨胀力传感器2）**
   - TX: PB3, RX: PB4
   - 与膨胀力传感器3完全相同

6. **USART6（膨胀力传感器4）**
   - TX: PC0, RX: PC1
   - 与膨胀力传感器3完全相同

7. **USART7（O2传感器）**
   - TX: PC6, RX: PC7
   - 与H2传感器完全相同

8. **USART8（CO2传感器）**
   - TX: PC2, RX: PC3
   - 读浓度指令: FF 03 60 01 00 02 9E 15
   - 返回数据: FF 03 04 00（高位） 00（低位） 77 B5 03 BB
   - 小数点个数为1，浓度要除以10

### CAN配置
- CAN TX: PA12, CAN RX: PA11
- 数据排列顺序: CO浓度、H2浓度、O2浓度、CO2浓度、膨胀力1、膨胀力2、膨胀力3、膨胀力4
- 分3帧发送，每帧8字节

### LED配置
- LED1（膨胀力传感器3）: PB9
- LED2（CO传感器）: PA4
- LED3（H2传感器）: PB0
- LED4（膨胀力传感器1）: PA15
- LED5（膨胀力传感器2）: PD2
- LED6（膨胀力传感器4）: PC15
- LED7（O2传感器）: PC8
- LED8（CO2传感器）: PA0
- LED CAN: PB12

注意: LED高电平熄灭，低电平点亮

## 软件架构

### 目录结构
```
├── Hardware/           # 硬件驱动层
│   ├── Can.c/h        # CAN通信驱动
│   ├── Delay.c/h      # 延时函数
│   ├── Hardware.c/h   # 硬件初始化
│   ├── Led.c/h        # LED控制
│   ├── Timer.c/h      # 定时器驱动
│   ├── Usart1.c/h     # USART1驱动
│   ├── Usart2.c/h     # USART2驱动
│   ├── Usart3.c/h     # USART3驱动
│   ├── Usart4.c/h     # USART4驱动
│   ├── Usart5.c/h     # USART5驱动
│   ├── Usart6.c/h     # USART6驱动
│   ├── Usart7.c/h     # USART7驱动
│   └── Usart8.c/h     # USART8驱动
├── Software/          # 软件应用层
│   ├── Software.c/h   # 软件初始化和数据管理
│   └── crc16.c/h      # CRC校验
├── User/              # 用户应用层
│   └── main.c         # 主程序
├── Start/             # 启动文件（不允许修改）
└── Library/           # 标准库（不允许修改）
```

### 数据流程
1. **500ms定时**: 发送传感器查询指令
2. **中断接收**: 各USART接收传感器数据并解析
3. **1s定时**: 打包数据并通过CAN发送
4. **LED指示**: 每次数据收发时对应LED闪烁

### 中断处理
- USART1、USART2: 独立中断处理
- USART3-8: 共享中断处理
- 定时器: TIM2中断，提供100ms、500ms、900ms、1s定时标志

## 编码格式
项目统一使用GB2312编码格式，确保中文注释正确显示。

## 优化特点
1. **模块化设计**: 每个USART独立驱动文件
2. **统一数据结构**: 所有USART使用相同的管理结构体
3. **LED状态指示**: 每个传感器对应独立LED指示
4. **DMA优化**: 合理分配DMA通道，避免冲突
5. **中断优化**: 根据硬件特性合理分配中断资源
6. **CAN通信优化**: 数据分帧传输，提高传输效率

## 注意事项
1. Start和Library目录不允许修改
2. H2和O2传感器上电默认主动发送，可不发送查询指令
3. DMA通道有限，部分USART使用轮询方式
4. LED为低电平点亮，高电平熄灭
5. 确保所有文件使用GB2312编码格式
