Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    stm32f0xx_it.o(i.TIM2_IRQHandler) refers to stm32f0xx_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    stm32f0xx_it.o(i.TIM2_IRQHandler) refers to stm32f0xx_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    stm32f0xx_it.o(i.TIM2_IRQHandler) refers to software.o(.data) for timer2_management
    stm32f0xx_it.o(i.USART3_8_IRQHandler) refers to stm32f0xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    stm32f0xx_it.o(i.USART3_8_IRQHandler) refers to stm32f0xx_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    stm32f0xx_it.o(i.USART3_8_IRQHandler) refers to aeabi_sdiv.o(.text) for __aeabi_idivmod
    stm32f0xx_it.o(i.USART3_8_IRQHandler) refers to led.o(i.led_flash) for led_flash
    stm32f0xx_it.o(i.USART3_8_IRQHandler) refers to rt_memclr.o(.text) for __aeabi_memclr
    stm32f0xx_it.o(i.USART3_8_IRQHandler) refers to stm32f0xx_it.o(.data) for usart3_rx_index
    stm32f0xx_it.o(i.USART3_8_IRQHandler) refers to software.o(.bss) for usart3_management
    system_stm32f0xx.o(i.SystemCoreClockUpdate) refers to aeabi_sdiv.o(.text) for __aeabi_uidivmod
    system_stm32f0xx.o(i.SystemCoreClockUpdate) refers to system_stm32f0xx.o(.data) for SystemCoreClock
    system_stm32f0xx.o(i.SystemInit) refers to system_stm32f0xx.o(i.SetSysClock) for SetSysClock
    startup_stm32f091.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f091.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f091.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f091.o(RESET) refers to startup_stm32f091.o(STACK) for __initial_sp
    startup_stm32f091.o(RESET) refers to startup_stm32f091.o(.text) for Reset_Handler
    startup_stm32f091.o(RESET) refers to stm32f0xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f091.o(RESET) refers to stm32f0xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f091.o(RESET) refers to stm32f0xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f091.o(RESET) refers to stm32f0xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f091.o(RESET) refers to stm32f0xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f091.o(RESET) refers to stm32f0xx_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f091.o(RESET) refers to usart1.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f091.o(RESET) refers to usart2.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f091.o(RESET) refers to stm32f0xx_it.o(i.USART3_8_IRQHandler) for USART3_8_IRQHandler
    startup_stm32f091.o(RESET) refers to can.o(i.CEC_CAN_IRQHandler) for CEC_CAN_IRQHandler
    startup_stm32f091.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f091.o(.text) refers to startup_stm32f091.o(STACK) for __initial_sp
    startup_stm32f091.o(.text) refers to system_stm32f0xx.o(i.SystemInit) for SystemInit
    startup_stm32f091.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f091.o(.text) refers to startup_stm32f091.o(HEAP) for Heap_Mem
    stm32f0xx_can.o(i.CAN_DeInit) refers to stm32f0xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f0xx_can.o(i.CAN_GetITStatus) refers to stm32f0xx_can.o(i.CheckITStatus) for CheckITStatus
    stm32f0xx_gpio.o(i.GPIO_DeInit) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphResetCmd) for RCC_AHBPeriphResetCmd
    stm32f0xx_rcc.o(i.RCC_GetClocksFreq) refers to aeabi_sdiv.o(.text) for __aeabi_uidivmod
    stm32f0xx_rcc.o(i.RCC_GetClocksFreq) refers to stm32f0xx_rcc.o(.data) for APBAHBPrescTable
    stm32f0xx_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f0xx_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f0xx_tim.o(i.TIM_DeInit) refers to stm32f0xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f0xx_tim.o(i.TIM_DeInit) refers to stm32f0xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f0xx_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f0xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f0xx_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f0xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f0xx_tim.o(i.TIM_ICInit) refers to stm32f0xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f0xx_tim.o(i.TIM_ICInit) refers to stm32f0xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f0xx_tim.o(i.TIM_ICInit) refers to stm32f0xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f0xx_tim.o(i.TIM_ICInit) refers to stm32f0xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f0xx_tim.o(i.TIM_ICInit) refers to stm32f0xx_tim.o(i.TI3_Config) for TI3_Config
    stm32f0xx_tim.o(i.TIM_ICInit) refers to stm32f0xx_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f0xx_tim.o(i.TIM_ICInit) refers to stm32f0xx_tim.o(i.TI4_Config) for TI4_Config
    stm32f0xx_tim.o(i.TIM_ICInit) refers to stm32f0xx_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f0xx_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f0xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f0xx_tim.o(i.TIM_PWMIConfig) refers to stm32f0xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f0xx_tim.o(i.TIM_PWMIConfig) refers to stm32f0xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f0xx_tim.o(i.TIM_PWMIConfig) refers to stm32f0xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f0xx_tim.o(i.TIM_PWMIConfig) refers to stm32f0xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f0xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f0xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f0xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f0xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f0xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f0xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f0xx_usart.o(i.USART_DeInit) refers to stm32f0xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f0xx_usart.o(i.USART_DeInit) refers to stm32f0xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f0xx_usart.o(i.USART_Init) refers to stm32f0xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f0xx_usart.o(i.USART_Init) refers to aeabi_sdiv.o(.text) for __aeabi_uidivmod
    main.o(i.main) refers to hardware.o(i.hardware_initializes) for hardware_initializes
    main.o(i.main) refers to software.o(i.software_initializes) for software_initializes
    main.o(i.main) refers to stm32f0xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    main.o(i.main) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to stm32f0xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    main.o(i.main) refers to led.o(i.led_off) for led_off
    main.o(i.main) refers to led.o(i.led_on) for led_on
    main.o(i.main) refers to led.o(i.led_toggle) for led_toggle
    main.o(i.main) refers to led.o(i.led_flash) for led_flash
    main.o(i.main) refers to software.o(.data) for timer2_management
    main.o(i.prepare_can_data) refers to aeabi_sdiv.o(.text) for __aeabi_idivmod
    main.o(i.prepare_can_data) refers to main.o(.data) for test_counter
    main.o(i.prepare_can_data) refers to software.o(.bss) for data_management
    main.o(i.send_sensor_queries) refers to usart2.o(i.usart2_send_data) for usart2_send_data
    main.o(i.send_sensor_queries) refers to usart1.o(i.usart1_send_data) for usart1_send_data
    main.o(i.send_sensor_queries) refers to usart4.o(i.usart4_send_data) for usart4_send_data
    main.o(i.send_sensor_queries) refers to usart5.o(i.usart5_send_data) for usart5_send_data
    main.o(i.send_sensor_queries) refers to usart6.o(i.usart6_send_data) for usart6_send_data
    main.o(i.send_sensor_queries) refers to usart8.o(i.usart8_send_data) for usart8_send_data
    main.o(i.send_sensor_queries) refers to main.o(.data) for co_query_cmd
    hardware.o(i.LED_turn) refers to stm32f0xx_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    hardware.o(i.LED_turn) refers to stm32f0xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    hardware.o(i.LED_turn) refers to stm32f0xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    hardware.o(i.hardware_initializes) refers to delay.o(i.delay_init) for delay_init
    hardware.o(i.hardware_initializes) refers to usart1.o(i.usart1_initializes) for usart1_initializes
    hardware.o(i.hardware_initializes) refers to usart2.o(i.usart2_initializes) for usart2_initializes
    hardware.o(i.hardware_initializes) refers to usart3.o(i.usart3_initializes) for usart3_initializes
    hardware.o(i.hardware_initializes) refers to usart4.o(i.usart4_initializes) for usart4_initializes
    hardware.o(i.hardware_initializes) refers to usart5.o(i.usart5_initializes) for usart5_initializes
    hardware.o(i.hardware_initializes) refers to usart6.o(i.usart6_initializes) for usart6_initializes
    hardware.o(i.hardware_initializes) refers to usart7.o(i.usart7_initializes) for usart7_initializes
    hardware.o(i.hardware_initializes) refers to usart8.o(i.usart8_initializes) for usart8_initializes
    hardware.o(i.hardware_initializes) refers to can.o(i.can_initializes) for can_initializes
    hardware.o(i.hardware_initializes) refers to timer.o(i.timer_initializes) for timer_initializes
    hardware.o(i.hardware_initializes) refers to led.o(i.led_initializes) for led_initializes
    delay.o(i.delay_init) refers to stm32f0xx_misc.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    can.o(i.CAN1_Receive) refers to stm32f0xx_can.o(i.CAN_MessagePending) for CAN_MessagePending
    can.o(i.CAN1_Receive) refers to stm32f0xx_can.o(i.CAN_Receive) for CAN_Receive
    can.o(i.CAN1_Receive) refers to can.o(.data) for j
    can.o(i.CAN1_Receive) refers to can.o(.bss) for buf1
    can.o(i.CAN_SendData) refers to rt_memcpy.o(.text) for __aeabi_memcpy
    can.o(i.CAN_SendData) refers to stm32f0xx_can.o(i.CAN_Transmit) for CAN_Transmit
    can.o(i.CAN_SendData) refers to stm32f0xx_can.o(i.CAN_TransmitStatus) for CAN_TransmitStatus
    can.o(i.CEC_CAN_IRQHandler) refers to stm32f0xx_can.o(i.CAN_GetITStatus) for CAN_GetITStatus
    can.o(i.CEC_CAN_IRQHandler) refers to stm32f0xx_can.o(i.CAN_ClearITPendingBit) for CAN_ClearITPendingBit
    can.o(i.CEC_CAN_IRQHandler) refers to can.o(i.CAN1_Receive) for CAN1_Receive
    can.o(i.CEC_CAN_IRQHandler) refers to can.o(.data) for j
    can.o(i.can_initializes) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    can.o(i.can_initializes) refers to stm32f0xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    can.o(i.can_initializes) refers to stm32f0xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    can.o(i.can_initializes) refers to stm32f0xx_gpio.o(i.GPIO_Init) for GPIO_Init
    can.o(i.can_initializes) refers to stm32f0xx_misc.o(i.NVIC_Init) for NVIC_Init
    can.o(i.can_initializes) refers to stm32f0xx_can.o(i.CAN_DeInit) for CAN_DeInit
    can.o(i.can_initializes) refers to stm32f0xx_can.o(i.CAN_StructInit) for CAN_StructInit
    can.o(i.can_initializes) refers to stm32f0xx_can.o(i.CAN_Init) for CAN_Init
    can.o(i.can_initializes) refers to stm32f0xx_can.o(i.CAN_FilterInit) for CAN_FilterInit
    can.o(i.can_initializes) refers to stm32f0xx_can.o(i.CAN_ClearFlag) for CAN_ClearFlag
    timer.o(i.timer_initializes) refers to stm32f0xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    timer.o(i.timer_initializes) refers to stm32f0xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer.o(i.timer_initializes) refers to stm32f0xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    timer.o(i.timer_initializes) refers to stm32f0xx_misc.o(i.NVIC_Init) for NVIC_Init
    timer.o(i.timer_initializes) refers to stm32f0xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    usart1.o(i.USART1_IRQHandler) refers to stm32f0xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart1.o(i.USART1_IRQHandler) refers to stm32f0xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    usart1.o(i.USART1_IRQHandler) refers to usart1.o(i.DMA_Enable) for DMA_Enable
    usart1.o(i.USART1_IRQHandler) refers to stm32f0xx_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    usart1.o(i.USART1_IRQHandler) refers to led.o(i.led_flash) for led_flash
    usart1.o(i.USART1_IRQHandler) refers to rt_memclr.o(.text) for __aeabi_memclr
    usart1.o(i.USART1_IRQHandler) refers to software.o(.bss) for usart1_management
    usart1.o(i.usart1_initializes) refers to stm32f0xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart1.o(i.usart1_initializes) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    usart1.o(i.usart1_initializes) refers to stm32f0xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart1.o(i.usart1_initializes) refers to stm32f0xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart1.o(i.usart1_initializes) refers to stm32f0xx_usart.o(i.USART_OverSampling8Cmd) for USART_OverSampling8Cmd
    usart1.o(i.usart1_initializes) refers to stm32f0xx_usart.o(i.USART_Init) for USART_Init
    usart1.o(i.usart1_initializes) refers to stm32f0xx_misc.o(i.NVIC_Init) for NVIC_Init
    usart1.o(i.usart1_initializes) refers to stm32f0xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart1.o(i.usart1_initializes) refers to stm32f0xx_usart.o(i.USART_DMACmd) for USART_DMACmd
    usart1.o(i.usart1_initializes) refers to stm32f0xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart1.o(i.usart1_initializes) refers to stm32f0xx_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    usart1.o(i.usart1_initializes) refers to usart1.o(i.usart_DMA_Config_TX) for usart_DMA_Config_TX
    usart1.o(i.usart1_initializes) refers to usart1.o(i.usart_DMA_Config_RX) for usart_DMA_Config_RX
    usart1.o(i.usart1_initializes) refers to software.o(.bss) for usart1_management
    usart1.o(i.usart1_send_data) refers to stm32f0xx_dma.o(i.DMA_ClearFlag) for DMA_ClearFlag
    usart1.o(i.usart1_send_data) refers to stm32f0xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    usart1.o(i.usart1_send_data) refers to rt_memcpy.o(.text) for __aeabi_memcpy
    usart1.o(i.usart1_send_data) refers to usart1.o(i.DMA_Enable) for DMA_Enable
    usart1.o(i.usart1_send_data) refers to software.o(.bss) for usart1_management
    usart1.o(i.usart_DMA_Config_RX) refers to stm32f0xx_dma.o(i.DMA_DeInit) for DMA_DeInit
    usart1.o(i.usart_DMA_Config_RX) refers to stm32f0xx_dma.o(i.DMA_Init) for DMA_Init
    usart1.o(i.usart_DMA_Config_RX) refers to stm32f0xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    usart1.o(i.usart_DMA_Config_TX) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    usart1.o(i.usart_DMA_Config_TX) refers to stm32f0xx_dma.o(i.DMA_Init) for DMA_Init
    usart2.o(i.USART2_IRQHandler) refers to stm32f0xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart2.o(i.USART2_IRQHandler) refers to stm32f0xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    usart2.o(i.USART2_IRQHandler) refers to usart1.o(i.DMA_Enable) for DMA_Enable
    usart2.o(i.USART2_IRQHandler) refers to stm32f0xx_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    usart2.o(i.USART2_IRQHandler) refers to aeabi_sdiv.o(.text) for __aeabi_idivmod
    usart2.o(i.USART2_IRQHandler) refers to led.o(i.led_flash) for led_flash
    usart2.o(i.USART2_IRQHandler) refers to rt_memclr.o(.text) for __aeabi_memclr
    usart2.o(i.USART2_IRQHandler) refers to software.o(.bss) for usart2_management
    usart2.o(i.usart2_initializes) refers to stm32f0xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    usart2.o(i.usart2_initializes) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    usart2.o(i.usart2_initializes) refers to stm32f0xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart2.o(i.usart2_initializes) refers to stm32f0xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart2.o(i.usart2_initializes) refers to stm32f0xx_usart.o(i.USART_OverSampling8Cmd) for USART_OverSampling8Cmd
    usart2.o(i.usart2_initializes) refers to stm32f0xx_usart.o(i.USART_Init) for USART_Init
    usart2.o(i.usart2_initializes) refers to stm32f0xx_misc.o(i.NVIC_Init) for NVIC_Init
    usart2.o(i.usart2_initializes) refers to stm32f0xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart2.o(i.usart2_initializes) refers to stm32f0xx_usart.o(i.USART_DMACmd) for USART_DMACmd
    usart2.o(i.usart2_initializes) refers to stm32f0xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart2.o(i.usart2_initializes) refers to stm32f0xx_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    usart2.o(i.usart2_initializes) refers to usart1.o(i.usart_DMA_Config_TX) for usart_DMA_Config_TX
    usart2.o(i.usart2_initializes) refers to usart1.o(i.usart_DMA_Config_RX) for usart_DMA_Config_RX
    usart2.o(i.usart2_initializes) refers to software.o(.bss) for usart2_management
    usart2.o(i.usart2_send_data) refers to stm32f0xx_dma.o(i.DMA_ClearFlag) for DMA_ClearFlag
    usart2.o(i.usart2_send_data) refers to stm32f0xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    usart2.o(i.usart2_send_data) refers to rt_memcpy.o(.text) for __aeabi_memcpy
    usart2.o(i.usart2_send_data) refers to usart1.o(i.DMA_Enable) for DMA_Enable
    usart2.o(i.usart2_send_data) refers to software.o(.bss) for usart2_management
    usart3.o(i.usart3_initializes) refers to stm32f0xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    usart3.o(i.usart3_initializes) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    usart3.o(i.usart3_initializes) refers to stm32f0xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart3.o(i.usart3_initializes) refers to stm32f0xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart3.o(i.usart3_initializes) refers to stm32f0xx_usart.o(i.USART_OverSampling8Cmd) for USART_OverSampling8Cmd
    usart3.o(i.usart3_initializes) refers to stm32f0xx_usart.o(i.USART_Init) for USART_Init
    usart3.o(i.usart3_initializes) refers to stm32f0xx_misc.o(i.NVIC_Init) for NVIC_Init
    usart3.o(i.usart3_initializes) refers to stm32f0xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart3.o(i.usart3_initializes) refers to stm32f0xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart3.o(i.usart3_initializes) refers to stm32f0xx_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    usart3.o(i.usart3_send_data) refers to stm32f0xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart3.o(i.usart3_send_data) refers to stm32f0xx_usart.o(i.USART_SendData) for USART_SendData
    usart4.o(i.usart4_initializes) refers to stm32f0xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    usart4.o(i.usart4_initializes) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    usart4.o(i.usart4_initializes) refers to stm32f0xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart4.o(i.usart4_initializes) refers to stm32f0xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart4.o(i.usart4_initializes) refers to stm32f0xx_usart.o(i.USART_OverSampling8Cmd) for USART_OverSampling8Cmd
    usart4.o(i.usart4_initializes) refers to stm32f0xx_usart.o(i.USART_Init) for USART_Init
    usart4.o(i.usart4_initializes) refers to stm32f0xx_misc.o(i.NVIC_Init) for NVIC_Init
    usart4.o(i.usart4_initializes) refers to stm32f0xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart4.o(i.usart4_initializes) refers to stm32f0xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart4.o(i.usart4_initializes) refers to stm32f0xx_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    usart4.o(i.usart4_send_data) refers to stm32f0xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart4.o(i.usart4_send_data) refers to stm32f0xx_usart.o(i.USART_SendData) for USART_SendData
    usart5.o(i.usart5_initializes) refers to stm32f0xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    usart5.o(i.usart5_initializes) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    usart5.o(i.usart5_initializes) refers to stm32f0xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart5.o(i.usart5_initializes) refers to stm32f0xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart5.o(i.usart5_initializes) refers to stm32f0xx_usart.o(i.USART_OverSampling8Cmd) for USART_OverSampling8Cmd
    usart5.o(i.usart5_initializes) refers to stm32f0xx_usart.o(i.USART_Init) for USART_Init
    usart5.o(i.usart5_initializes) refers to stm32f0xx_misc.o(i.NVIC_Init) for NVIC_Init
    usart5.o(i.usart5_initializes) refers to stm32f0xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart5.o(i.usart5_initializes) refers to stm32f0xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart5.o(i.usart5_initializes) refers to stm32f0xx_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    usart5.o(i.usart5_send_data) refers to stm32f0xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart5.o(i.usart5_send_data) refers to stm32f0xx_usart.o(i.USART_SendData) for USART_SendData
    usart6.o(i.usart6_initializes) refers to stm32f0xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart6.o(i.usart6_initializes) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    usart6.o(i.usart6_initializes) refers to stm32f0xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart6.o(i.usart6_initializes) refers to stm32f0xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart6.o(i.usart6_initializes) refers to stm32f0xx_usart.o(i.USART_OverSampling8Cmd) for USART_OverSampling8Cmd
    usart6.o(i.usart6_initializes) refers to stm32f0xx_usart.o(i.USART_Init) for USART_Init
    usart6.o(i.usart6_initializes) refers to stm32f0xx_misc.o(i.NVIC_Init) for NVIC_Init
    usart6.o(i.usart6_initializes) refers to stm32f0xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart6.o(i.usart6_initializes) refers to stm32f0xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart6.o(i.usart6_initializes) refers to stm32f0xx_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    usart6.o(i.usart6_send_data) refers to stm32f0xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart6.o(i.usart6_send_data) refers to stm32f0xx_usart.o(i.USART_SendData) for USART_SendData
    usart7.o(i.usart7_initializes) refers to stm32f0xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart7.o(i.usart7_initializes) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    usart7.o(i.usart7_initializes) refers to stm32f0xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart7.o(i.usart7_initializes) refers to stm32f0xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart7.o(i.usart7_initializes) refers to stm32f0xx_usart.o(i.USART_OverSampling8Cmd) for USART_OverSampling8Cmd
    usart7.o(i.usart7_initializes) refers to stm32f0xx_usart.o(i.USART_Init) for USART_Init
    usart7.o(i.usart7_initializes) refers to stm32f0xx_misc.o(i.NVIC_Init) for NVIC_Init
    usart7.o(i.usart7_initializes) refers to stm32f0xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart7.o(i.usart7_initializes) refers to stm32f0xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart7.o(i.usart7_initializes) refers to stm32f0xx_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    usart7.o(i.usart7_send_data) refers to stm32f0xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart7.o(i.usart7_send_data) refers to stm32f0xx_usart.o(i.USART_SendData) for USART_SendData
    usart8.o(i.usart8_initializes) refers to stm32f0xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart8.o(i.usart8_initializes) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    usart8.o(i.usart8_initializes) refers to stm32f0xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart8.o(i.usart8_initializes) refers to stm32f0xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart8.o(i.usart8_initializes) refers to stm32f0xx_usart.o(i.USART_OverSampling8Cmd) for USART_OverSampling8Cmd
    usart8.o(i.usart8_initializes) refers to stm32f0xx_usart.o(i.USART_Init) for USART_Init
    usart8.o(i.usart8_initializes) refers to stm32f0xx_misc.o(i.NVIC_Init) for NVIC_Init
    usart8.o(i.usart8_initializes) refers to stm32f0xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart8.o(i.usart8_initializes) refers to stm32f0xx_usart.o(i.USART_DMACmd) for USART_DMACmd
    usart8.o(i.usart8_initializes) refers to stm32f0xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart8.o(i.usart8_initializes) refers to stm32f0xx_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    usart8.o(i.usart8_send_data) refers to stm32f0xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart8.o(i.usart8_send_data) refers to stm32f0xx_usart.o(i.USART_SendData) for USART_SendData
    led.o(i.led_flash) refers to led.o(i.led_on) for led_on
    led.o(i.led_flash) refers to delay.o(i.delay_ms) for delay_ms
    led.o(i.led_flash) refers to led.o(i.led_off) for led_off
    led.o(i.led_initializes) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    led.o(i.led_initializes) refers to stm32f0xx_gpio.o(i.GPIO_Init) for GPIO_Init
    led.o(i.led_initializes) refers to stm32f0xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.led_initializes) refers to led.o(.constdata) for led_configs
    led.o(i.led_off) refers to stm32f0xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.led_off) refers to led.o(.constdata) for led_configs
    led.o(i.led_on) refers to stm32f0xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    led.o(i.led_on) refers to led.o(.constdata) for led_configs
    led.o(i.led_toggle) refers to stm32f0xx_gpio.o(i.GPIO_ReadOutputDataBit) for GPIO_ReadOutputDataBit
    led.o(i.led_toggle) refers to stm32f0xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    led.o(i.led_toggle) refers to stm32f0xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.led_toggle) refers to led.o(.constdata) for led_configs
    software.o(i.software_initializes) refers to rt_memclr.o(.text) for __aeabi_memclr4
    software.o(i.software_initializes) refers to software.o(.bss) for usart1_management
    software.o(i.software_initializes) refers to software.o(.data) for timer2_management
    crc16.o(i.Crc16) refers to crc16.o(.constdata) for u16CrcTalbeAbs
    rt_memcpy.o(.text) refers to rt_memcpy.o(.emb_text) for __aeabi_memcpy4
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_idiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f091.o(.text) for __user_initial_stackheap
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to tempstk.o(.text) for __temporary_stack_top
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to __main.o(!!!main) for __main
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing stm32f0xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_it.o(.revsh_text), (4 bytes).
    Removing system_stm32f0xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f0xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f0xx.o(i.SystemCoreClockUpdate), (176 bytes).
    Removing system_stm32f0xx.o(.data), (20 bytes).
    Removing stm32f0xx_can.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_can.o(.revsh_text), (4 bytes).
    Removing stm32f0xx_can.o(i.CAN_CancelTransmit), (54 bytes).
    Removing stm32f0xx_can.o(i.CAN_DBGFreeze), (28 bytes).
    Removing stm32f0xx_can.o(i.CAN_FIFORelease), (24 bytes).
    Removing stm32f0xx_can.o(i.CAN_GetFlagStatus), (146 bytes).
    Removing stm32f0xx_can.o(i.CAN_GetLSBTransmitErrorCounter), (16 bytes).
    Removing stm32f0xx_can.o(i.CAN_GetLastErrorCode), (14 bytes).
    Removing stm32f0xx_can.o(i.CAN_GetReceiveErrorCounter), (10 bytes).
    Removing stm32f0xx_can.o(i.CAN_ITConfig), (20 bytes).
    Removing stm32f0xx_can.o(i.CAN_OperatingModeRequest), (172 bytes).
    Removing stm32f0xx_can.o(i.CAN_SlaveStartBank), (56 bytes).
    Removing stm32f0xx_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f0xx_can.o(i.CAN_TTComModeCmd), (128 bytes).
    Removing stm32f0xx_can.o(i.CAN_Transmit), (308 bytes).
    Removing stm32f0xx_can.o(i.CAN_TransmitStatus), (176 bytes).
    Removing stm32f0xx_can.o(i.CAN_WakeUp), (52 bytes).
    Removing stm32f0xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f0xx_dma.o(i.DMA_ClearFlag), (32 bytes).
    Removing stm32f0xx_dma.o(i.DMA_ClearITPendingBit), (32 bytes).
    Removing stm32f0xx_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f0xx_dma.o(i.DMA_GetFlagStatus), (52 bytes).
    Removing stm32f0xx_dma.o(i.DMA_GetITStatus), (52 bytes).
    Removing stm32f0xx_dma.o(i.DMA_ITConfig), (20 bytes).
    Removing stm32f0xx_dma.o(i.DMA_RemapConfig), (52 bytes).
    Removing stm32f0xx_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f0xx_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f0xx_exti.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_exti.o(.revsh_text), (4 bytes).
    Removing stm32f0xx_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f0xx_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f0xx_exti.o(i.EXTI_DeInit), (32 bytes).
    Removing stm32f0xx_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f0xx_exti.o(i.EXTI_GetFlagStatus), (28 bytes).
    Removing stm32f0xx_exti.o(i.EXTI_GetITStatus), (28 bytes).
    Removing stm32f0xx_exti.o(i.EXTI_Init), (136 bytes).
    Removing stm32f0xx_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f0xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_DeInit), (176 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_PinLockConfig), (34 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_ReadInputData), (6 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_ReadInputDataBit), (20 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_ReadOutputData), (6 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_StructInit), (24 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_WriteBit), (12 bytes).
    Removing stm32f0xx_misc.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_misc.o(.revsh_text), (4 bytes).
    Removing stm32f0xx_misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing stm32f0xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_ADCCLKConfig), (56 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_AHBPeriphResetCmd), (32 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_AdjustHSI14CalibrationValue), (32 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_AdjustHSICalibrationValue), (32 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_BackupResetCmd), (40 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_CECCLKConfig), (28 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_ClockSecuritySystemCmd), (40 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_DeInit), (120 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_GetFlagStatus), (72 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_GetITStatus), (28 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_HCLKConfig), (32 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_HSEConfig), (16 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_HSI14ADCRequestCmd), (36 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_HSI14Cmd), (36 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_HSI48Cmd), (40 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_HSICmd), (36 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_I2CCLKConfig), (28 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_LSEConfig), (40 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_LSEDriveConfig), (28 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_LSICmd), (36 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_MCOConfig), (28 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_PCLKConfig), (36 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_PLLCmd), (40 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_PLLConfig), (36 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_PREDIV1Config), (28 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_RTCCLKCmd), (40 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_SYSCLKConfig), (28 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_USARTCLKConfig), (72 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_USBCLKConfig), (28 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_WaitForHSEStartUp), (60 bytes).
    Removing stm32f0xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f0xx_tim.o(i.TI1_Config), (56 bytes).
    Removing stm32f0xx_tim.o(i.TI2_Config), (76 bytes).
    Removing stm32f0xx_tim.o(i.TI3_Config), (72 bytes).
    Removing stm32f0xx_tim.o(i.TI4_Config), (80 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ARRPreloadConfig), (28 bytes).
    Removing stm32f0xx_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f0xx_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f0xx_tim.o(i.TIM_CCPreloadControl), (28 bytes).
    Removing stm32f0xx_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f0xx_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ClearOC1Ref), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ClearOC2Ref), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ClearOC3Ref), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ClearOC4Ref), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_CounterModeConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_CtrlPWMOutputs), (34 bytes).
    Removing stm32f0xx_tim.o(i.TIM_DMACmd), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_DMAConfig), (12 bytes).
    Removing stm32f0xx_tim.o(i.TIM_DeInit), (260 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ETRClockMode1Config), (52 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ETRClockMode2Config), (34 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ETRConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_EncoderInterfaceConfig), (68 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ForcedOC1Config), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ForcedOC2Config), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ForcedOC3Config), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ForcedOC4Config), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f0xx_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_GetCapture4), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_GetFlagStatus), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ICInit), (112 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_InternalClockConfig), (16 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC1FastConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC1Init), (148 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC1NPolarityConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC1PolarityConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC1PreloadConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC2FastConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC2Init), (172 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC2NPolarityConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC2PolarityConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC2PreloadConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC3FastConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC3Init), (152 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC3NPolarityConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC3PolarityConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC3PreloadConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC4FastConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC4Init), (112 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC4PolarityConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC4PreloadConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f0xx_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_RemapConfig), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectCCDMA), (28 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectCOM), (28 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectHallSensor), (28 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectInputTrigger), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectMasterSlaveMode), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectOCREFClear), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectOCxM), (82 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectOnePulseMode), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectOutputTrigger), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectSlaveMode), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetClockDivision), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetCompare4), (4 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetIC1Prescaler), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetIC2Prescaler), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetIC3Prescaler), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetIC4Prescaler), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_TIxExternalClockConfig), (58 bytes).
    Removing stm32f0xx_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f0xx_tim.o(i.TIM_UpdateDisableConfig), (28 bytes).
    Removing stm32f0xx_tim.o(i.TIM_UpdateRequestConfig), (28 bytes).
    Removing stm32f0xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f0xx_usart.o(i.USART_AddressDetectionConfig), (16 bytes).
    Removing stm32f0xx_usart.o(i.USART_AutoBaudRateCmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_AutoBaudRateConfig), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_ClockInit), (36 bytes).
    Removing stm32f0xx_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f0xx_usart.o(i.USART_DECmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_DEPolarityConfig), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_DMAReceptionErrorConfig), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_DataInvCmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_DeInit), (240 bytes).
    Removing stm32f0xx_usart.o(i.USART_DirectionModeCmd), (20 bytes).
    Removing stm32f0xx_usart.o(i.USART_GetITStatus), (78 bytes).
    Removing stm32f0xx_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f0xx_usart.o(i.USART_InvPinCmd), (20 bytes).
    Removing stm32f0xx_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f0xx_usart.o(i.USART_IrDAConfig), (16 bytes).
    Removing stm32f0xx_usart.o(i.USART_LINBreakDetectLengthConfig), (16 bytes).
    Removing stm32f0xx_usart.o(i.USART_LINCmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_MSBFirstCmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_MuteModeCmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_MuteModeWakeUpConfig), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_OneBitMethodCmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_OverrunDetectionConfig), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_ReceiverTimeOutCmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_RequestCmd), (20 bytes).
    Removing stm32f0xx_usart.o(i.USART_STOPModeCmd), (24 bytes).
    Removing stm32f0xx_usart.o(i.USART_SWAPPinCmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f0xx_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_SetAutoRetryCount), (20 bytes).
    Removing stm32f0xx_usart.o(i.USART_SetBlockLength), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_SetDEAssertionTime), (20 bytes).
    Removing stm32f0xx_usart.o(i.USART_SetDEDeassertionTime), (20 bytes).
    Removing stm32f0xx_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f0xx_usart.o(i.USART_SetPrescaler), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_SetReceiverTimeOut), (16 bytes).
    Removing stm32f0xx_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f0xx_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f0xx_usart.o(i.USART_StopModeWakeUpSourceConfig), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_StructInit), (24 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(i.prepare_can_data), (412 bytes).
    Removing main.o(i.send_sensor_queries), (64 bytes).
    Removing main.o(.data), (40 bytes).
    Removing hardware.o(.rev16_text), (4 bytes).
    Removing hardware.o(.revsh_text), (4 bytes).
    Removing hardware.o(i.LED_turn), (44 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(i.delay_us), (52 bytes).
    Removing can.o(.rev16_text), (4 bytes).
    Removing can.o(.revsh_text), (4 bytes).
    Removing can.o(i.CAN_SendData), (124 bytes).
    Removing timer.o(.rev16_text), (4 bytes).
    Removing timer.o(.revsh_text), (4 bytes).
    Removing usart1.o(.rev16_text), (4 bytes).
    Removing usart1.o(.revsh_text), (4 bytes).
    Removing usart1.o(i.usart1_send_data), (48 bytes).
    Removing usart2.o(.rev16_text), (4 bytes).
    Removing usart2.o(.revsh_text), (4 bytes).
    Removing usart2.o(i.usart2_send_data), (52 bytes).
    Removing usart3.o(.rev16_text), (4 bytes).
    Removing usart3.o(.revsh_text), (4 bytes).
    Removing usart3.o(i.usart3_send_data), (60 bytes).
    Removing usart4.o(.rev16_text), (4 bytes).
    Removing usart4.o(.revsh_text), (4 bytes).
    Removing usart4.o(i.usart4_send_data), (60 bytes).
    Removing usart5.o(.rev16_text), (4 bytes).
    Removing usart5.o(.revsh_text), (4 bytes).
    Removing usart5.o(i.usart5_send_data), (60 bytes).
    Removing usart6.o(.rev16_text), (4 bytes).
    Removing usart6.o(.revsh_text), (4 bytes).
    Removing usart6.o(i.usart6_send_data), (60 bytes).
    Removing usart7.o(.rev16_text), (4 bytes).
    Removing usart7.o(.revsh_text), (4 bytes).
    Removing usart7.o(i.usart7_send_data), (60 bytes).
    Removing usart8.o(.rev16_text), (4 bytes).
    Removing usart8.o(.revsh_text), (4 bytes).
    Removing usart8.o(i.usart8_send_data), (60 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing software.o(.rev16_text), (4 bytes).
    Removing software.o(.revsh_text), (4 bytes).
    Removing crc16.o(i.Crc16), (72 bytes).
    Removing crc16.o(.constdata), (32 bytes).

273 unused section(s) (total 9318 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0_sigfpe.o ABSOLUTE
    ../clib/angel/scatterp.s                 0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  tempstk.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdiv.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdiv_div0.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memcpy.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memcpy.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/cfplib/fpinit.c                 0x00000000   Number         0  fpinit.o ABSOLUTE
    Hardware\Can.c                           0x00000000   Number         0  can.o ABSOLUTE
    Hardware\Delay.c                         0x00000000   Number         0  delay.o ABSOLUTE
    Hardware\Hardware.c                      0x00000000   Number         0  hardware.o ABSOLUTE
    Hardware\Led.c                           0x00000000   Number         0  led.o ABSOLUTE
    Hardware\Timer.c                         0x00000000   Number         0  timer.o ABSOLUTE
    Hardware\Usart1.c                        0x00000000   Number         0  usart1.o ABSOLUTE
    Hardware\Usart2.c                        0x00000000   Number         0  usart2.o ABSOLUTE
    Hardware\Usart3.c                        0x00000000   Number         0  usart3.o ABSOLUTE
    Hardware\Usart4.c                        0x00000000   Number         0  usart4.o ABSOLUTE
    Hardware\Usart5.c                        0x00000000   Number         0  usart5.o ABSOLUTE
    Hardware\Usart6.c                        0x00000000   Number         0  usart6.o ABSOLUTE
    Hardware\Usart7.c                        0x00000000   Number         0  usart7.o ABSOLUTE
    Hardware\Usart8.c                        0x00000000   Number         0  usart8.o ABSOLUTE
    Hardware\\Can.c                          0x00000000   Number         0  can.o ABSOLUTE
    Hardware\\Delay.c                        0x00000000   Number         0  delay.o ABSOLUTE
    Hardware\\Hardware.c                     0x00000000   Number         0  hardware.o ABSOLUTE
    Hardware\\Led.c                          0x00000000   Number         0  led.o ABSOLUTE
    Hardware\\Timer.c                        0x00000000   Number         0  timer.o ABSOLUTE
    Hardware\\Usart1.c                       0x00000000   Number         0  usart1.o ABSOLUTE
    Hardware\\Usart2.c                       0x00000000   Number         0  usart2.o ABSOLUTE
    Hardware\\Usart3.c                       0x00000000   Number         0  usart3.o ABSOLUTE
    Hardware\\Usart4.c                       0x00000000   Number         0  usart4.o ABSOLUTE
    Hardware\\Usart5.c                       0x00000000   Number         0  usart5.o ABSOLUTE
    Hardware\\Usart6.c                       0x00000000   Number         0  usart6.o ABSOLUTE
    Hardware\\Usart7.c                       0x00000000   Number         0  usart7.o ABSOLUTE
    Hardware\\Usart8.c                       0x00000000   Number         0  usart8.o ABSOLUTE
    Library\\stm32f0xx_can.c                 0x00000000   Number         0  stm32f0xx_can.o ABSOLUTE
    Library\\stm32f0xx_dma.c                 0x00000000   Number         0  stm32f0xx_dma.o ABSOLUTE
    Library\\stm32f0xx_exti.c                0x00000000   Number         0  stm32f0xx_exti.o ABSOLUTE
    Library\\stm32f0xx_gpio.c                0x00000000   Number         0  stm32f0xx_gpio.o ABSOLUTE
    Library\\stm32f0xx_misc.c                0x00000000   Number         0  stm32f0xx_misc.o ABSOLUTE
    Library\\stm32f0xx_rcc.c                 0x00000000   Number         0  stm32f0xx_rcc.o ABSOLUTE
    Library\\stm32f0xx_tim.c                 0x00000000   Number         0  stm32f0xx_tim.o ABSOLUTE
    Library\\stm32f0xx_usart.c               0x00000000   Number         0  stm32f0xx_usart.o ABSOLUTE
    Library\stm32f0xx_can.c                  0x00000000   Number         0  stm32f0xx_can.o ABSOLUTE
    Library\stm32f0xx_dma.c                  0x00000000   Number         0  stm32f0xx_dma.o ABSOLUTE
    Library\stm32f0xx_exti.c                 0x00000000   Number         0  stm32f0xx_exti.o ABSOLUTE
    Library\stm32f0xx_gpio.c                 0x00000000   Number         0  stm32f0xx_gpio.o ABSOLUTE
    Library\stm32f0xx_misc.c                 0x00000000   Number         0  stm32f0xx_misc.o ABSOLUTE
    Library\stm32f0xx_rcc.c                  0x00000000   Number         0  stm32f0xx_rcc.o ABSOLUTE
    Library\stm32f0xx_tim.c                  0x00000000   Number         0  stm32f0xx_tim.o ABSOLUTE
    Library\stm32f0xx_usart.c                0x00000000   Number         0  stm32f0xx_usart.o ABSOLUTE
    Software\Software.c                      0x00000000   Number         0  software.o ABSOLUTE
    Software\\Software.c                     0x00000000   Number         0  software.o ABSOLUTE
    Software\crc16.c                         0x00000000   Number         0  crc16.o ABSOLUTE
    Start\\stm32f0xx_it.c                    0x00000000   Number         0  stm32f0xx_it.o ABSOLUTE
    Start\\system_stm32f0xx.c                0x00000000   Number         0  system_stm32f0xx.o ABSOLUTE
    Start\startup_stm32f091.s                0x00000000   Number         0  startup_stm32f091.o ABSOLUTE
    Start\stm32f0xx_it.c                     0x00000000   Number         0  stm32f0xx_it.o ABSOLUTE
    Start\system_stm32f0xx.c                 0x00000000   Number         0  system_stm32f0xx.o ABSOLUTE
    User\\main.c                             0x00000000   Number         0  main.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      188  startup_stm32f091.o(RESET)
    !!!main                                  0x080000bc   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000c4   Section       60  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000100   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x0800011c   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x08000138   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800013a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800013a   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x0800013c   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0800013e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0800013e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x0800013e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x0800013e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x0800013e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x0800013e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x0800013e   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000140   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000140   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000140   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000146   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000146   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800014a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800014a   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000152   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000154   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000154   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000158   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000160   Section      116  startup_stm32f091.o(.text)
    .text                                    0x080001d4   Section        0  rt_memclr.o(.text)
    .text                                    0x08000214   Section      346  aeabi_sdiv.o(.text)
    .text                                    0x0800036e   Section        0  heapauxi.o(.text)
    .text                                    0x08000374   Section       62  sys_stackheap_outer.o(.text)
    .text                                    0x080003b2   Section        0  exit.o(.text)
    .text                                    0x080003c4   Section        8  libspace.o(.text)
    .text                                    0x080003cc   Section        0  sys_exit.o(.text)
    .text                                    0x080003d8   Section        2  use_no_semi.o(.text)
    .text                                    0x080003da   Section        0  indicate_semi.o(.text)
    i.CAN1_Receive                           0x080003dc   Section        0  can.o(i.CAN1_Receive)
    i.CAN_ClearFlag                          0x08000428   Section        0  stm32f0xx_can.o(i.CAN_ClearFlag)
    i.CAN_ClearITPendingBit                  0x0800046c   Section        0  stm32f0xx_can.o(i.CAN_ClearITPendingBit)
    i.CAN_DeInit                             0x08000528   Section        0  stm32f0xx_can.o(i.CAN_DeInit)
    i.CAN_FilterInit                         0x08000540   Section        0  stm32f0xx_can.o(i.CAN_FilterInit)
    i.CAN_GetITStatus                        0x0800063c   Section        0  stm32f0xx_can.o(i.CAN_GetITStatus)
    i.CAN_Init                               0x08000774   Section        0  stm32f0xx_can.o(i.CAN_Init)
    i.CAN_MessagePending                     0x08000890   Section        0  stm32f0xx_can.o(i.CAN_MessagePending)
    i.CAN_Receive                            0x080008b0   Section        0  stm32f0xx_can.o(i.CAN_Receive)
    i.CAN_StructInit                         0x080009b6   Section        0  stm32f0xx_can.o(i.CAN_StructInit)
    i.CEC_CAN_IRQHandler                     0x080009d8   Section        0  can.o(i.CEC_CAN_IRQHandler)
    i.CheckITStatus                          0x08000a10   Section        0  stm32f0xx_can.o(i.CheckITStatus)
    CheckITStatus                            0x08000a11   Thumb Code    20  stm32f0xx_can.o(i.CheckITStatus)
    i.DMA_Cmd                                0x08000a24   Section        0  stm32f0xx_dma.o(i.DMA_Cmd)
    i.DMA_DeInit                             0x08000a40   Section        0  stm32f0xx_dma.o(i.DMA_DeInit)
    i.DMA_Enable                             0x08000ba4   Section        0  usart1.o(i.DMA_Enable)
    i.DMA_Init                               0x08000bb8   Section        0  stm32f0xx_dma.o(i.DMA_Init)
    i.GPIO_Init                              0x08000bf8   Section        0  stm32f0xx_gpio.o(i.GPIO_Init)
    i.GPIO_PinAFConfig                       0x08000c88   Section        0  stm32f0xx_gpio.o(i.GPIO_PinAFConfig)
    i.GPIO_ReadOutputDataBit                 0x08000ccc   Section        0  stm32f0xx_gpio.o(i.GPIO_ReadOutputDataBit)
    i.GPIO_ResetBits                         0x08000ce0   Section        0  stm32f0xx_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x08000ce4   Section        0  stm32f0xx_gpio.o(i.GPIO_SetBits)
    i.HardFault_Handler                      0x08000ce8   Section        0  stm32f0xx_it.o(i.HardFault_Handler)
    i.NMI_Handler                            0x08000cec   Section        0  stm32f0xx_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08000cf0   Section        0  stm32f0xx_misc.o(i.NVIC_Init)
    i.PendSV_Handler                         0x08000d60   Section        0  stm32f0xx_it.o(i.PendSV_Handler)
    i.RCC_AHBPeriphClockCmd                  0x08000d64   Section        0  stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd)
    i.RCC_APB1PeriphClockCmd                 0x08000d84   Section        0  stm32f0xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB1PeriphResetCmd                 0x08000da4   Section        0  stm32f0xx_rcc.o(i.RCC_APB1PeriphResetCmd)
    i.RCC_APB2PeriphClockCmd                 0x08000dc4   Section        0  stm32f0xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x08000de4   Section        0  stm32f0xx_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x0800102c   Section        0  stm32f0xx_it.o(i.SVC_Handler)
    i.SetSysClock                            0x08001030   Section        0  system_stm32f0xx.o(i.SetSysClock)
    SetSysClock                              0x08001031   Thumb Code   206  system_stm32f0xx.o(i.SetSysClock)
    i.SysTick_CLKSourceConfig                0x08001108   Section        0  stm32f0xx_misc.o(i.SysTick_CLKSourceConfig)
    i.SysTick_Handler                        0x0800112c   Section        0  stm32f0xx_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08001130   Section        0  system_stm32f0xx.o(i.SystemInit)
    i.TIM2_IRQHandler                        0x080011ac   Section        0  stm32f0xx_it.o(i.TIM2_IRQHandler)
    i.TIM_ClearITPendingBit                  0x08001200   Section        0  stm32f0xx_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_Cmd                                0x08001208   Section        0  stm32f0xx_tim.o(i.TIM_Cmd)
    i.TIM_GetITStatus                        0x08001224   Section        0  stm32f0xx_tim.o(i.TIM_GetITStatus)
    i.TIM_ITConfig                           0x0800124a   Section        0  stm32f0xx_tim.o(i.TIM_ITConfig)
    i.TIM_TimeBaseInit                       0x08001260   Section        0  stm32f0xx_tim.o(i.TIM_TimeBaseInit)
    i.USART1_IRQHandler                      0x080012dc   Section        0  usart1.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x08001360   Section        0  usart2.o(i.USART2_IRQHandler)
    i.USART3_8_IRQHandler                    0x080013e0   Section        0  stm32f0xx_it.o(i.USART3_8_IRQHandler)
    i.USART_ClearFlag                        0x08001794   Section        0  stm32f0xx_usart.o(i.USART_ClearFlag)
    i.USART_ClearITPendingBit                0x08001798   Section        0  stm32f0xx_usart.o(i.USART_ClearITPendingBit)
    i.USART_Cmd                              0x080017aa   Section        0  stm32f0xx_usart.o(i.USART_Cmd)
    i.USART_DMACmd                           0x080017c2   Section        0  stm32f0xx_usart.o(i.USART_DMACmd)
    i.USART_GetFlagStatus                    0x080017d6   Section        0  stm32f0xx_usart.o(i.USART_GetFlagStatus)
    i.USART_ITConfig                         0x080017ea   Section        0  stm32f0xx_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x0800182c   Section        0  stm32f0xx_usart.o(i.USART_Init)
    i.USART_OverSampling8Cmd                 0x08001924   Section        0  stm32f0xx_usart.o(i.USART_OverSampling8Cmd)
    i.USART_ReceiveData                      0x08001940   Section        0  stm32f0xx_usart.o(i.USART_ReceiveData)
    i.can_initializes                        0x0800194c   Section        0  can.o(i.can_initializes)
    i.delay_init                             0x08001a3c   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x08001a48   Section        0  delay.o(i.delay_ms)
    i.hardware_initializes                   0x08001a84   Section        0  hardware.o(i.hardware_initializes)
    i.led_flash                              0x08001ab8   Section        0  led.o(i.led_flash)
    i.led_initializes                        0x08001ad4   Section        0  led.o(i.led_initializes)
    i.led_off                                0x08001b2c   Section        0  led.o(i.led_off)
    i.led_on                                 0x08001b4c   Section        0  led.o(i.led_on)
    i.led_toggle                             0x08001b6c   Section        0  led.o(i.led_toggle)
    i.main                                   0x08001bb0   Section        0  main.o(i.main)
    i.software_initializes                   0x08001ce0   Section        0  software.o(i.software_initializes)
    i.timer_initializes                      0x08001d5c   Section        0  timer.o(i.timer_initializes)
    i.usart1_initializes                     0x08001db4   Section        0  usart1.o(i.usart1_initializes)
    i.usart2_initializes                     0x08001e90   Section        0  usart2.o(i.usart2_initializes)
    i.usart3_initializes                     0x08001f6c   Section        0  usart3.o(i.usart3_initializes)
    i.usart4_initializes                     0x08002010   Section        0  usart4.o(i.usart4_initializes)
    i.usart5_initializes                     0x080020b8   Section        0  usart5.o(i.usart5_initializes)
    i.usart6_initializes                     0x0800215c   Section        0  usart6.o(i.usart6_initializes)
    i.usart7_initializes                     0x08002200   Section        0  usart7.o(i.usart7_initializes)
    i.usart8_initializes                     0x080022a4   Section        0  usart8.o(i.usart8_initializes)
    i.usart_DMA_Config_RX                    0x08002360   Section        0  usart1.o(i.usart_DMA_Config_RX)
    i.usart_DMA_Config_TX                    0x080023aa   Section        0  usart1.o(i.usart_DMA_Config_TX)
    .constdata                               0x080023f0   Section       72  led.o(.constdata)
    led_configs                              0x080023f0   Data          72  led.o(.constdata)
    .data                                    0x20000000   Section        6  stm32f0xx_it.o(.data)
    usart3_rx_index                          0x20000000   Data           1  stm32f0xx_it.o(.data)
    usart4_rx_index                          0x20000001   Data           1  stm32f0xx_it.o(.data)
    usart5_rx_index                          0x20000002   Data           1  stm32f0xx_it.o(.data)
    usart6_rx_index                          0x20000003   Data           1  stm32f0xx_it.o(.data)
    usart7_rx_index                          0x20000004   Data           1  stm32f0xx_it.o(.data)
    usart8_rx_index                          0x20000005   Data           1  stm32f0xx_it.o(.data)
    .data                                    0x20000006   Section       16  stm32f0xx_rcc.o(.data)
    APBAHBPrescTable                         0x20000006   Data          16  stm32f0xx_rcc.o(.data)
    .data                                    0x20000016   Section        1  can.o(.data)
    .data                                    0x20000017   Section        5  software.o(.data)
    .bss                                     0x2000001c   Section       48  can.o(.bss)
    .bss                                     0x2000004c   Section      376  software.o(.bss)
    .bss                                     0x200001c4   Section       96  libspace.o(.bss)
    HEAP                                     0x20000228   Section      512  startup_stm32f091.o(HEAP)
    Heap_Mem                                 0x20000228   Data         512  startup_stm32f091.o(HEAP)
    STACK                                    0x20000428   Section     1024  startup_stm32f091.o(STACK)
    Stack_Mem                                0x20000428   Data        1024  startup_stm32f091.o(STACK)
    __initial_sp                             0x20000828   Data           0  startup_stm32f091.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000bc   Number         0  startup_stm32f091.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f091.o(RESET)
    __Vectors_End                            0x080000bc   Data           0  startup_stm32f091.o(RESET)
    __main                                   0x080000bd   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000c5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000c5   Thumb Code    52  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000c5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x080000d5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000101   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x0800011d   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x08000139   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x0800013b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x0800013d   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0800013f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0800013f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x0800013f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x0800013f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x0800013f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x0800013f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x0800013f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000141   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000141   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000141   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000147   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000147   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800014b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800014b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000153   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000155   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000155   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000159   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000161   Thumb Code    38  startup_stm32f091.o(.text)
    ADC1_COMP_IRQHandler                     0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    DMA1_Ch1_IRQHandler                      0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    DMA1_Ch2_3_DMA2_Ch1_2_IRQHandler         0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    DMA1_Ch4_7_DMA2_Ch3_5_IRQHandler         0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    EXTI0_1_IRQHandler                       0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    EXTI2_3_IRQHandler                       0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    EXTI4_15_IRQHandler                      0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    FLASH_IRQHandler                         0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    I2C1_IRQHandler                          0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    I2C2_IRQHandler                          0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    PVD_VDDIO2_IRQHandler                    0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    RCC_CRS_IRQHandler                       0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    RTC_IRQHandler                           0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    SPI1_IRQHandler                          0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    SPI2_IRQHandler                          0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    TIM14_IRQHandler                         0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    TIM15_IRQHandler                         0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    TIM16_IRQHandler                         0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    TIM17_IRQHandler                         0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    TIM1_BRK_UP_TRG_COM_IRQHandler           0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    TIM1_CC_IRQHandler                       0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    TIM3_IRQHandler                          0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    TIM6_DAC_IRQHandler                      0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    TIM7_IRQHandler                          0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    TSC_IRQHandler                           0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    WWDG_IRQHandler                          0x08000191   Thumb Code     0  startup_stm32f091.o(.text)
    __user_initial_stackheap                 0x08000195   Thumb Code     0  startup_stm32f091.o(.text)
    _memset_w                                0x080001d5   Thumb Code    26  rt_memclr.o(.text)
    _memset                                  0x080001ef   Thumb Code    30  rt_memclr.o(.text)
    __aeabi_memclr                           0x0800020d   Thumb Code     4  rt_memclr.o(.text)
    __rt_memclr                              0x0800020d   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x08000211   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr8                          0x08000211   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr_w                            0x08000211   Thumb Code     4  rt_memclr.o(.text)
    __aeabi_uidiv                            0x08000215   Thumb Code     0  aeabi_sdiv.o(.text)
    __aeabi_uidivmod                         0x08000215   Thumb Code    20  aeabi_sdiv.o(.text)
    __aeabi_idiv                             0x08000229   Thumb Code     0  aeabi_sdiv.o(.text)
    __aeabi_idivmod                          0x08000229   Thumb Code   326  aeabi_sdiv.o(.text)
    __use_two_region_memory                  0x0800036f   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000371   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000373   Thumb Code     2  heapauxi.o(.text)
    __user_setup_stackheap                   0x08000375   Thumb Code    62  sys_stackheap_outer.o(.text)
    exit                                     0x080003b3   Thumb Code    16  exit.o(.text)
    __user_libspace                          0x080003c5   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080003c5   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080003c5   Thumb Code     0  libspace.o(.text)
    _sys_exit                                0x080003cd   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x080003d9   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080003d9   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x080003db   Thumb Code     0  indicate_semi.o(.text)
    CAN1_Receive                             0x080003dd   Thumb Code    64  can.o(i.CAN1_Receive)
    CAN_ClearFlag                            0x08000429   Thumb Code    64  stm32f0xx_can.o(i.CAN_ClearFlag)
    CAN_ClearITPendingBit                    0x0800046d   Thumb Code   184  stm32f0xx_can.o(i.CAN_ClearITPendingBit)
    CAN_DeInit                               0x08000529   Thumb Code    24  stm32f0xx_can.o(i.CAN_DeInit)
    CAN_FilterInit                           0x08000541   Thumb Code   244  stm32f0xx_can.o(i.CAN_FilterInit)
    CAN_GetITStatus                          0x0800063d   Thumb Code   306  stm32f0xx_can.o(i.CAN_GetITStatus)
    CAN_Init                                 0x08000775   Thumb Code   280  stm32f0xx_can.o(i.CAN_Init)
    CAN_MessagePending                       0x08000891   Thumb Code    32  stm32f0xx_can.o(i.CAN_MessagePending)
    CAN_Receive                              0x080008b1   Thumb Code   262  stm32f0xx_can.o(i.CAN_Receive)
    CAN_StructInit                           0x080009b7   Thumb Code    32  stm32f0xx_can.o(i.CAN_StructInit)
    CEC_CAN_IRQHandler                       0x080009d9   Thumb Code    48  can.o(i.CEC_CAN_IRQHandler)
    DMA_Cmd                                  0x08000a25   Thumb Code    24  stm32f0xx_dma.o(i.DMA_Cmd)
    DMA_DeInit                               0x08000a41   Thumb Code   342  stm32f0xx_dma.o(i.DMA_DeInit)
    DMA_Enable                               0x08000ba5   Thumb Code    20  usart1.o(i.DMA_Enable)
    DMA_Init                                 0x08000bb9   Thumb Code    58  stm32f0xx_dma.o(i.DMA_Init)
    GPIO_Init                                0x08000bf9   Thumb Code   144  stm32f0xx_gpio.o(i.GPIO_Init)
    GPIO_PinAFConfig                         0x08000c89   Thumb Code    68  stm32f0xx_gpio.o(i.GPIO_PinAFConfig)
    GPIO_ReadOutputDataBit                   0x08000ccd   Thumb Code    20  stm32f0xx_gpio.o(i.GPIO_ReadOutputDataBit)
    GPIO_ResetBits                           0x08000ce1   Thumb Code     4  stm32f0xx_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x08000ce5   Thumb Code     4  stm32f0xx_gpio.o(i.GPIO_SetBits)
    HardFault_Handler                        0x08000ce9   Thumb Code     4  stm32f0xx_it.o(i.HardFault_Handler)
    NMI_Handler                              0x08000ced   Thumb Code     2  stm32f0xx_it.o(i.NMI_Handler)
    NVIC_Init                                0x08000cf1   Thumb Code   106  stm32f0xx_misc.o(i.NVIC_Init)
    PendSV_Handler                           0x08000d61   Thumb Code     2  stm32f0xx_it.o(i.PendSV_Handler)
    RCC_AHBPeriphClockCmd                    0x08000d65   Thumb Code    28  stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd)
    RCC_APB1PeriphClockCmd                   0x08000d85   Thumb Code    28  stm32f0xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB1PeriphResetCmd                   0x08000da5   Thumb Code    28  stm32f0xx_rcc.o(i.RCC_APB1PeriphResetCmd)
    RCC_APB2PeriphClockCmd                   0x08000dc5   Thumb Code    28  stm32f0xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x08000de5   Thumb Code   554  stm32f0xx_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x0800102d   Thumb Code     2  stm32f0xx_it.o(i.SVC_Handler)
    SysTick_CLKSourceConfig                  0x08001109   Thumb Code    32  stm32f0xx_misc.o(i.SysTick_CLKSourceConfig)
    SysTick_Handler                          0x0800112d   Thumb Code     2  stm32f0xx_it.o(i.SysTick_Handler)
    SystemInit                               0x08001131   Thumb Code   110  system_stm32f0xx.o(i.SystemInit)
    TIM2_IRQHandler                          0x080011ad   Thumb Code    80  stm32f0xx_it.o(i.TIM2_IRQHandler)
    TIM_ClearITPendingBit                    0x08001201   Thumb Code     6  stm32f0xx_tim.o(i.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x08001209   Thumb Code    24  stm32f0xx_tim.o(i.TIM_Cmd)
    TIM_GetITStatus                          0x08001225   Thumb Code    38  stm32f0xx_tim.o(i.TIM_GetITStatus)
    TIM_ITConfig                             0x0800124b   Thumb Code    20  stm32f0xx_tim.o(i.TIM_ITConfig)
    TIM_TimeBaseInit                         0x08001261   Thumb Code    90  stm32f0xx_tim.o(i.TIM_TimeBaseInit)
    USART1_IRQHandler                        0x080012dd   Thumb Code   112  usart1.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x08001361   Thumb Code   108  usart2.o(i.USART2_IRQHandler)
    USART3_8_IRQHandler                      0x080013e1   Thumb Code   872  stm32f0xx_it.o(i.USART3_8_IRQHandler)
    USART_ClearFlag                          0x08001795   Thumb Code     4  stm32f0xx_usart.o(i.USART_ClearFlag)
    USART_ClearITPendingBit                  0x08001799   Thumb Code    18  stm32f0xx_usart.o(i.USART_ClearITPendingBit)
    USART_Cmd                                0x080017ab   Thumb Code    24  stm32f0xx_usart.o(i.USART_Cmd)
    USART_DMACmd                             0x080017c3   Thumb Code    20  stm32f0xx_usart.o(i.USART_DMACmd)
    USART_GetFlagStatus                      0x080017d7   Thumb Code    20  stm32f0xx_usart.o(i.USART_GetFlagStatus)
    USART_ITConfig                           0x080017eb   Thumb Code    66  stm32f0xx_usart.o(i.USART_ITConfig)
    USART_Init                               0x0800182d   Thumb Code   226  stm32f0xx_usart.o(i.USART_Init)
    USART_OverSampling8Cmd                   0x08001925   Thumb Code    28  stm32f0xx_usart.o(i.USART_OverSampling8Cmd)
    USART_ReceiveData                        0x08001941   Thumb Code    10  stm32f0xx_usart.o(i.USART_ReceiveData)
    can_initializes                          0x0800194d   Thumb Code   230  can.o(i.can_initializes)
    delay_init                               0x08001a3d   Thumb Code    12  delay.o(i.delay_init)
    delay_ms                                 0x08001a49   Thumb Code    54  delay.o(i.delay_ms)
    hardware_initializes                     0x08001a85   Thumb Code    52  hardware.o(i.hardware_initializes)
    led_flash                                0x08001ab9   Thumb Code    28  led.o(i.led_flash)
    led_initializes                          0x08001ad5   Thumb Code    82  led.o(i.led_initializes)
    led_off                                  0x08001b2d   Thumb Code    26  led.o(i.led_off)
    led_on                                   0x08001b4d   Thumb Code    26  led.o(i.led_on)
    led_toggle                               0x08001b6d   Thumb Code    64  led.o(i.led_toggle)
    main                                     0x08001bb1   Thumb Code   296  main.o(i.main)
    software_initializes                     0x08001ce1   Thumb Code    84  software.o(i.software_initializes)
    timer_initializes                        0x08001d5d   Thumb Code    78  timer.o(i.timer_initializes)
    usart1_initializes                       0x08001db5   Thumb Code   202  usart1.o(i.usart1_initializes)
    usart2_initializes                       0x08001e91   Thumb Code   202  usart2.o(i.usart2_initializes)
    usart3_initializes                       0x08001f6d   Thumb Code   152  usart3.o(i.usart3_initializes)
    usart4_initializes                       0x08002011   Thumb Code   154  usart4.o(i.usart4_initializes)
    usart5_initializes                       0x080020b9   Thumb Code   152  usart5.o(i.usart5_initializes)
    usart6_initializes                       0x0800215d   Thumb Code   152  usart6.o(i.usart6_initializes)
    usart7_initializes                       0x08002201   Thumb Code   152  usart7.o(i.usart7_initializes)
    usart8_initializes                       0x080022a5   Thumb Code   172  usart8.o(i.usart8_initializes)
    usart_DMA_Config_RX                      0x08002361   Thumb Code    74  usart1.o(i.usart_DMA_Config_RX)
    usart_DMA_Config_TX                      0x080023ab   Thumb Code    68  usart1.o(i.usart_DMA_Config_TX)
    Region$$Table$$Base                      0x08002438   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08002458   Number         0  anon$$obj.o(Region$$Table)
    j                                        0x20000016   Data           1  can.o(.data)
    timer2_management                        0x20000017   Data           5  software.o(.data)
    buf1                                     0x2000001c   Data          48  can.o(.bss)
    usart1_management                        0x2000004c   Data          40  software.o(.bss)
    usart2_management                        0x20000074   Data          40  software.o(.bss)
    usart3_management                        0x2000009c   Data          40  software.o(.bss)
    usart4_management                        0x200000c4   Data          40  software.o(.bss)
    usart5_management                        0x200000ec   Data          40  software.o(.bss)
    usart6_management                        0x20000114   Data          40  software.o(.bss)
    usart7_management                        0x2000013c   Data          40  software.o(.bss)
    usart8_management                        0x20000164   Data          40  software.o(.bss)
    data_management                          0x2000018c   Data          56  software.o(.bss)
    __libspace_start                         0x200001c4   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000224   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000bd

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00002474, Max: 0x00040000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00002458, Max: 0x00040000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000bc   Data   RO          265    RESET               startup_stm32f091.o
    0x080000bc   0x080000bc   0x00000008   Code   RO         2399  * !!!main             c_p.l(__main.o)
    0x080000c4   0x080000c4   0x0000003c   Code   RO         2568    !!!scatter          c_p.l(__scatter.o)
    0x08000100   0x08000100   0x0000001a   Code   RO         2570    !!handler_copy      c_p.l(__scatter_copy.o)
    0x0800011a   0x0800011a   0x00000002   PAD
    0x0800011c   0x0800011c   0x0000001c   Code   RO         2572    !!handler_zi        c_p.l(__scatter_zi.o)
    0x08000138   0x08000138   0x00000002   Code   RO         2437    .ARM.Collect$$libinit$$00000000  c_p.l(libinit.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2451    .ARM.Collect$$libinit$$00000002  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2453    .ARM.Collect$$libinit$$00000004  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2456    .ARM.Collect$$libinit$$0000000A  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2458    .ARM.Collect$$libinit$$0000000C  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2460    .ARM.Collect$$libinit$$0000000E  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2463    .ARM.Collect$$libinit$$00000011  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2465    .ARM.Collect$$libinit$$00000013  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2467    .ARM.Collect$$libinit$$00000015  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2469    .ARM.Collect$$libinit$$00000017  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2471    .ARM.Collect$$libinit$$00000019  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2473    .ARM.Collect$$libinit$$0000001B  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2475    .ARM.Collect$$libinit$$0000001D  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2477    .ARM.Collect$$libinit$$0000001F  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2479    .ARM.Collect$$libinit$$00000021  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2481    .ARM.Collect$$libinit$$00000023  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2483    .ARM.Collect$$libinit$$00000025  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2487    .ARM.Collect$$libinit$$0000002C  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2489    .ARM.Collect$$libinit$$0000002E  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2491    .ARM.Collect$$libinit$$00000030  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000000   Code   RO         2493    .ARM.Collect$$libinit$$00000032  c_p.l(libinit2.o)
    0x0800013a   0x0800013a   0x00000002   Code   RO         2494    .ARM.Collect$$libinit$$00000033  c_p.l(libinit2.o)
    0x0800013c   0x0800013c   0x00000002   Code   RO         2525    .ARM.Collect$$libshutdown$$00000000  c_p.l(libshutdown.o)
    0x0800013e   0x0800013e   0x00000000   Code   RO         2551    .ARM.Collect$$libshutdown$$00000002  c_p.l(libshutdown2.o)
    0x0800013e   0x0800013e   0x00000000   Code   RO         2553    .ARM.Collect$$libshutdown$$00000004  c_p.l(libshutdown2.o)
    0x0800013e   0x0800013e   0x00000000   Code   RO         2556    .ARM.Collect$$libshutdown$$00000007  c_p.l(libshutdown2.o)
    0x0800013e   0x0800013e   0x00000000   Code   RO         2559    .ARM.Collect$$libshutdown$$0000000A  c_p.l(libshutdown2.o)
    0x0800013e   0x0800013e   0x00000000   Code   RO         2561    .ARM.Collect$$libshutdown$$0000000C  c_p.l(libshutdown2.o)
    0x0800013e   0x0800013e   0x00000000   Code   RO         2564    .ARM.Collect$$libshutdown$$0000000F  c_p.l(libshutdown2.o)
    0x0800013e   0x0800013e   0x00000002   Code   RO         2565    .ARM.Collect$$libshutdown$$00000010  c_p.l(libshutdown2.o)
    0x08000140   0x08000140   0x00000000   Code   RO         2401    .ARM.Collect$$rtentry$$00000000  c_p.l(__rtentry.o)
    0x08000140   0x08000140   0x00000000   Code   RO         2407    .ARM.Collect$$rtentry$$00000002  c_p.l(__rtentry2.o)
    0x08000140   0x08000140   0x00000006   Code   RO         2419    .ARM.Collect$$rtentry$$00000004  c_p.l(__rtentry4.o)
    0x08000146   0x08000146   0x00000000   Code   RO         2409    .ARM.Collect$$rtentry$$00000009  c_p.l(__rtentry2.o)
    0x08000146   0x08000146   0x00000004   Code   RO         2410    .ARM.Collect$$rtentry$$0000000A  c_p.l(__rtentry2.o)
    0x0800014a   0x0800014a   0x00000000   Code   RO         2412    .ARM.Collect$$rtentry$$0000000C  c_p.l(__rtentry2.o)
    0x0800014a   0x0800014a   0x00000008   Code   RO         2413    .ARM.Collect$$rtentry$$0000000D  c_p.l(__rtentry2.o)
    0x08000152   0x08000152   0x00000002   Code   RO         2442    .ARM.Collect$$rtexit$$00000000  c_p.l(rtexit.o)
    0x08000154   0x08000154   0x00000000   Code   RO         2498    .ARM.Collect$$rtexit$$00000002  c_p.l(rtexit2.o)
    0x08000154   0x08000154   0x00000004   Code   RO         2499    .ARM.Collect$$rtexit$$00000003  c_p.l(rtexit2.o)
    0x08000158   0x08000158   0x00000006   Code   RO         2500    .ARM.Collect$$rtexit$$00000004  c_p.l(rtexit2.o)
    0x0800015e   0x0800015e   0x00000002   PAD
    0x08000160   0x08000160   0x00000074   Code   RO          266    .text               startup_stm32f091.o
    0x080001d4   0x080001d4   0x00000040   Code   RO         2391    .text               c_p.l(rt_memclr.o)
    0x08000214   0x08000214   0x0000015a   Code   RO         2393    .text               c_p.l(aeabi_sdiv.o)
    0x0800036e   0x0800036e   0x00000006   Code   RO         2397    .text               c_p.l(heapauxi.o)
    0x08000374   0x08000374   0x0000003e   Code   RO         2423    .text               c_p.l(sys_stackheap_outer.o)
    0x080003b2   0x080003b2   0x00000010   Code   RO         2426    .text               c_p.l(exit.o)
    0x080003c2   0x080003c2   0x00000002   PAD
    0x080003c4   0x080003c4   0x00000008   Code   RO         2438    .text               c_p.l(libspace.o)
    0x080003cc   0x080003cc   0x0000000c   Code   RO         2495    .text               c_p.l(sys_exit.o)
    0x080003d8   0x080003d8   0x00000002   Code   RO         2514    .text               c_p.l(use_no_semi.o)
    0x080003da   0x080003da   0x00000000   Code   RO         2516    .text               c_p.l(indicate_semi.o)
    0x080003da   0x080003da   0x00000002   PAD
    0x080003dc   0x080003dc   0x0000004c   Code   RO         2001    i.CAN1_Receive      can.o
    0x08000428   0x08000428   0x00000044   Code   RO          273    i.CAN_ClearFlag     stm32f0xx_can.o
    0x0800046c   0x0800046c   0x000000bc   Code   RO          274    i.CAN_ClearITPendingBit  stm32f0xx_can.o
    0x08000528   0x08000528   0x00000018   Code   RO          276    i.CAN_DeInit        stm32f0xx_can.o
    0x08000540   0x08000540   0x000000fc   Code   RO          278    i.CAN_FilterInit    stm32f0xx_can.o
    0x0800063c   0x0800063c   0x00000138   Code   RO          280    i.CAN_GetITStatus   stm32f0xx_can.o
    0x08000774   0x08000774   0x0000011c   Code   RO          285    i.CAN_Init          stm32f0xx_can.o
    0x08000890   0x08000890   0x00000020   Code   RO          286    i.CAN_MessagePending  stm32f0xx_can.o
    0x080008b0   0x080008b0   0x00000106   Code   RO          288    i.CAN_Receive       stm32f0xx_can.o
    0x080009b6   0x080009b6   0x00000020   Code   RO          291    i.CAN_StructInit    stm32f0xx_can.o
    0x080009d6   0x080009d6   0x00000002   PAD
    0x080009d8   0x080009d8   0x00000038   Code   RO         2003    i.CEC_CAN_IRQHandler  can.o
    0x08000a10   0x08000a10   0x00000014   Code   RO          296    i.CheckITStatus     stm32f0xx_can.o
    0x08000a24   0x08000a24   0x0000001c   Code   RO          440    i.DMA_Cmd           stm32f0xx_dma.o
    0x08000a40   0x08000a40   0x00000164   Code   RO          441    i.DMA_DeInit        stm32f0xx_dma.o
    0x08000ba4   0x08000ba4   0x00000014   Code   RO         2063    i.DMA_Enable        usart1.o
    0x08000bb8   0x08000bb8   0x00000040   Code   RO          446    i.DMA_Init          stm32f0xx_dma.o
    0x08000bf8   0x08000bf8   0x00000090   Code   RO          593    i.GPIO_Init         stm32f0xx_gpio.o
    0x08000c88   0x08000c88   0x00000044   Code   RO          594    i.GPIO_PinAFConfig  stm32f0xx_gpio.o
    0x08000ccc   0x08000ccc   0x00000014   Code   RO          599    i.GPIO_ReadOutputDataBit  stm32f0xx_gpio.o
    0x08000ce0   0x08000ce0   0x00000004   Code   RO          600    i.GPIO_ResetBits    stm32f0xx_gpio.o
    0x08000ce4   0x08000ce4   0x00000004   Code   RO          601    i.GPIO_SetBits      stm32f0xx_gpio.o
    0x08000ce8   0x08000ce8   0x00000004   Code   RO            3    i.HardFault_Handler  stm32f0xx_it.o
    0x08000cec   0x08000cec   0x00000002   Code   RO            4    i.NMI_Handler       stm32f0xx_it.o
    0x08000cee   0x08000cee   0x00000002   PAD
    0x08000cf0   0x08000cf0   0x00000070   Code   RO          687    i.NVIC_Init         stm32f0xx_misc.o
    0x08000d60   0x08000d60   0x00000002   Code   RO            5    i.PendSV_Handler    stm32f0xx_it.o
    0x08000d62   0x08000d62   0x00000002   PAD
    0x08000d64   0x08000d64   0x00000020   Code   RO          723    i.RCC_AHBPeriphClockCmd  stm32f0xx_rcc.o
    0x08000d84   0x08000d84   0x00000020   Code   RO          725    i.RCC_APB1PeriphClockCmd  stm32f0xx_rcc.o
    0x08000da4   0x08000da4   0x00000020   Code   RO          726    i.RCC_APB1PeriphResetCmd  stm32f0xx_rcc.o
    0x08000dc4   0x08000dc4   0x00000020   Code   RO          727    i.RCC_APB2PeriphClockCmd  stm32f0xx_rcc.o
    0x08000de4   0x08000de4   0x00000248   Code   RO          737    i.RCC_GetClocksFreq  stm32f0xx_rcc.o
    0x0800102c   0x0800102c   0x00000002   Code   RO            6    i.SVC_Handler       stm32f0xx_it.o
    0x0800102e   0x0800102e   0x00000002   PAD
    0x08001030   0x08001030   0x000000d8   Code   RO          207    i.SetSysClock       system_stm32f0xx.o
    0x08001108   0x08001108   0x00000024   Code   RO          689    i.SysTick_CLKSourceConfig  stm32f0xx_misc.o
    0x0800112c   0x0800112c   0x00000002   Code   RO            7    i.SysTick_Handler   stm32f0xx_it.o
    0x0800112e   0x0800112e   0x00000002   PAD
    0x08001130   0x08001130   0x0000007c   Code   RO          209    i.SystemInit        system_stm32f0xx.o
    0x080011ac   0x080011ac   0x00000054   Code   RO            8    i.TIM2_IRQHandler   stm32f0xx_it.o
    0x08001200   0x08001200   0x00000006   Code   RO          998    i.TIM_ClearITPendingBit  stm32f0xx_tim.o
    0x08001206   0x08001206   0x00000002   PAD
    0x08001208   0x08001208   0x0000001c   Code   RO         1003    i.TIM_Cmd           stm32f0xx_tim.o
    0x08001224   0x08001224   0x00000026   Code   RO         1024    i.TIM_GetITStatus   stm32f0xx_tim.o
    0x0800124a   0x0800124a   0x00000014   Code   RO         1028    i.TIM_ITConfig      stm32f0xx_tim.o
    0x0800125e   0x0800125e   0x00000002   PAD
    0x08001260   0x08001260   0x0000007c   Code   RO         1076    i.TIM_TimeBaseInit  stm32f0xx_tim.o
    0x080012dc   0x080012dc   0x00000084   Code   RO         2064    i.USART1_IRQHandler  usart1.o
    0x08001360   0x08001360   0x00000080   Code   RO         2113    i.USART2_IRQHandler  usart2.o
    0x080013e0   0x080013e0   0x000003b4   Code   RO            9    i.USART3_8_IRQHandler  stm32f0xx_it.o
    0x08001794   0x08001794   0x00000004   Code   RO         1561    i.USART_ClearFlag   stm32f0xx_usart.o
    0x08001798   0x08001798   0x00000012   Code   RO         1562    i.USART_ClearITPendingBit  stm32f0xx_usart.o
    0x080017aa   0x080017aa   0x00000018   Code   RO         1565    i.USART_Cmd         stm32f0xx_usart.o
    0x080017c2   0x080017c2   0x00000014   Code   RO         1568    i.USART_DMACmd      stm32f0xx_usart.o
    0x080017d6   0x080017d6   0x00000014   Code   RO         1573    i.USART_GetFlagStatus  stm32f0xx_usart.o
    0x080017ea   0x080017ea   0x00000042   Code   RO         1576    i.USART_ITConfig    stm32f0xx_usart.o
    0x0800182c   0x0800182c   0x000000f8   Code   RO         1577    i.USART_Init        stm32f0xx_usart.o
    0x08001924   0x08001924   0x0000001c   Code   RO         1587    i.USART_OverSampling8Cmd  stm32f0xx_usart.o
    0x08001940   0x08001940   0x0000000a   Code   RO         1589    i.USART_ReceiveData  stm32f0xx_usart.o
    0x0800194a   0x0800194a   0x00000002   PAD
    0x0800194c   0x0800194c   0x000000f0   Code   RO         2004    i.can_initializes   can.o
    0x08001a3c   0x08001a3c   0x0000000c   Code   RO         1969    i.delay_init        delay.o
    0x08001a48   0x08001a48   0x0000003c   Code   RO         1970    i.delay_ms          delay.o
    0x08001a84   0x08001a84   0x00000034   Code   RO         1905    i.hardware_initializes  hardware.o
    0x08001ab8   0x08001ab8   0x0000001c   Code   RO         2301    i.led_flash         led.o
    0x08001ad4   0x08001ad4   0x00000058   Code   RO         2302    i.led_initializes   led.o
    0x08001b2c   0x08001b2c   0x00000020   Code   RO         2303    i.led_off           led.o
    0x08001b4c   0x08001b4c   0x00000020   Code   RO         2304    i.led_on            led.o
    0x08001b6c   0x08001b6c   0x00000044   Code   RO         2305    i.led_toggle        led.o
    0x08001bb0   0x08001bb0   0x00000130   Code   RO         1869    i.main              main.o
    0x08001ce0   0x08001ce0   0x0000007c   Code   RO         2347    i.software_initializes  software.o
    0x08001d5c   0x08001d5c   0x00000058   Code   RO         2043    i.timer_initializes  timer.o
    0x08001db4   0x08001db4   0x000000dc   Code   RO         2065    i.usart1_initializes  usart1.o
    0x08001e90   0x08001e90   0x000000dc   Code   RO         2114    i.usart2_initializes  usart2.o
    0x08001f6c   0x08001f6c   0x000000a4   Code   RO         2145    i.usart3_initializes  usart3.o
    0x08002010   0x08002010   0x000000a8   Code   RO         2171    i.usart4_initializes  usart4.o
    0x080020b8   0x080020b8   0x000000a4   Code   RO         2197    i.usart5_initializes  usart5.o
    0x0800215c   0x0800215c   0x000000a4   Code   RO         2223    i.usart6_initializes  usart6.o
    0x08002200   0x08002200   0x000000a4   Code   RO         2249    i.usart7_initializes  usart7.o
    0x080022a4   0x080022a4   0x000000bc   Code   RO         2275    i.usart8_initializes  usart8.o
    0x08002360   0x08002360   0x0000004a   Code   RO         2067    i.usart_DMA_Config_RX  usart1.o
    0x080023aa   0x080023aa   0x00000044   Code   RO         2068    i.usart_DMA_Config_TX  usart1.o
    0x080023ee   0x080023ee   0x00000002   PAD
    0x080023f0   0x080023f0   0x00000048   Data   RO         2306    .constdata          led.o
    0x08002438   0x08002438   0x00000020   Data   RO         2566    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08002458, Size: 0x00000828, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08002458   0x00000006   Data   RW           10    .data               stm32f0xx_it.o
    0x20000006   0x0800245e   0x00000010   Data   RW          763    .data               stm32f0xx_rcc.o
    0x20000016   0x0800246e   0x00000001   Data   RW         2006    .data               can.o
    0x20000017   0x0800246f   0x00000005   Data   RW         2349    .data               software.o
    0x2000001c        -       0x00000030   Zero   RW         2005    .bss                can.o
    0x2000004c        -       0x00000178   Zero   RW         2348    .bss                software.o
    0x200001c4        -       0x00000060   Zero   RW         2439    .bss                c_p.l(libspace.o)
    0x20000224   0x08002474   0x00000004   PAD
    0x20000228        -       0x00000200   Zero   RW          264    HEAP                startup_stm32f091.o
    0x20000428        -       0x00000400   Zero   RW          263    STACK               startup_stm32f091.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       372         30          0          1         48       2331   can.o
        72          6          0          0          0        972   delay.o
        52          0          0          0          0       1363   hardware.o
       248         22         72          0          0       3094   led.o
       304          8          0          0          0        671   main.o
       124         40          0          5        376       2079   software.o
       116         56        188          0       1536        636   startup_stm32f091.o
      1474         26          0          0          0      15327   stm32f0xx_can.o
       448         24          0          0          0      30654   stm32f0xx_dma.o
       240          0          0          0          0       7688   stm32f0xx_gpio.o
      1044         80          0          6          0     250407   stm32f0xx_it.o
       148         10          0          0          0       2310   stm32f0xx_misc.o
       712         46          0         16          0      17007   stm32f0xx_rcc.o
       216         38          0          0          0      23358   stm32f0xx_tim.o
       438         22          0          0          0      15776   stm32f0xx_usart.o
       340         24          0          0          0       1389   system_stm32f0xx.o
        88         10          0          0          0        560   timer.o
       514         38          0          0          0       3474   usart1.o
       348         38          0          0          0       1249   usart2.o
       164         12          0          0          0        661   usart3.o
       168         14          0          0          0        661   usart4.o
       164         12          0          0          0        661   usart5.o
       164         12          0          0          0        661   usart6.o
       164         12          0          0          0        661   usart7.o
       188         16          0          0          0        669   usart8.o

    ----------------------------------------------------------------------
      8328        <USER>        <GROUP>         28       1960     384319   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        18          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        60          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
       346          0          0          0          0         92   aeabi_sdiv.o
        16          0          0          0          0         68   exit.o
         6          0          0          0          0        136   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        64          0          0          0          0        108   rt_memclr.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         60   sys_exit.o
        62          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o

    ----------------------------------------------------------------------
       684         <USER>          <GROUP>          0        100        748   Library Totals
         8          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       676         16          0          0         96        748   c_p.l

    ----------------------------------------------------------------------
       684         <USER>          <GROUP>          0        100        748   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      9012        612        292         28       2060     381711   Grand Totals
      9012        612        292         28       2060     381711   ELF Image Totals
      9012        612        292         28          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 9304 (   9.09kB)
    Total RW  Size (RW Data + ZI Data)              2088 (   2.04kB)
    Total ROM Size (Code + RO Data + RW Data)       9332 (   9.11kB)

==============================================================================

