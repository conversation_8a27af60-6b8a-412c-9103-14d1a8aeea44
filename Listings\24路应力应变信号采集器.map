Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    stm32f0xx_it.o(i.USART3_8_IRQHandler) refers to stm32f0xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    stm32f0xx_it.o(i.USART3_8_IRQHandler) refers to stm32f0xx_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    stm32f0xx_it.o(i.USART3_8_IRQHandler) refers to aeabi_sdiv.o(.text) for __aeabi_idivmod
    stm32f0xx_it.o(i.USART3_8_IRQHandler) refers to rt_memclr.o(.text) for __aeabi_memclr
    stm32f0xx_it.o(i.USART3_8_IRQHandler) refers to stm32f0xx_it.o(.data) for usart3_rx_index
    stm32f0xx_it.o(i.USART3_8_IRQHandler) refers to software.o(.bss) for usart3_management
    system_stm32f0xx.o(i.SystemCoreClockUpdate) refers to aeabi_sdiv.o(.text) for __aeabi_uidivmod
    system_stm32f0xx.o(i.SystemCoreClockUpdate) refers to system_stm32f0xx.o(.data) for SystemCoreClock
    system_stm32f0xx.o(i.SystemInit) refers to system_stm32f0xx.o(i.SetSysClock) for SetSysClock
    startup_stm32f091.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f091.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f091.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f091.o(RESET) refers to startup_stm32f091.o(STACK) for __initial_sp
    startup_stm32f091.o(RESET) refers to startup_stm32f091.o(.text) for Reset_Handler
    startup_stm32f091.o(RESET) refers to stm32f0xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f091.o(RESET) refers to stm32f0xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f091.o(RESET) refers to stm32f0xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f091.o(RESET) refers to stm32f0xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f091.o(RESET) refers to stm32f0xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f091.o(RESET) refers to timer.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f091.o(RESET) refers to usart1.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f091.o(RESET) refers to usart2.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f091.o(RESET) refers to stm32f0xx_it.o(i.USART3_8_IRQHandler) for USART3_8_IRQHandler
    startup_stm32f091.o(RESET) refers to can.o(i.CEC_CAN_IRQHandler) for CEC_CAN_IRQHandler
    startup_stm32f091.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f091.o(.text) refers to startup_stm32f091.o(STACK) for __initial_sp
    startup_stm32f091.o(.text) refers to system_stm32f0xx.o(i.SystemInit) for SystemInit
    startup_stm32f091.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f091.o(.text) refers to startup_stm32f091.o(HEAP) for Heap_Mem
    stm32f0xx_can.o(i.CAN_DeInit) refers to stm32f0xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f0xx_can.o(i.CAN_GetITStatus) refers to stm32f0xx_can.o(i.CheckITStatus) for CheckITStatus
    stm32f0xx_gpio.o(i.GPIO_DeInit) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphResetCmd) for RCC_AHBPeriphResetCmd
    stm32f0xx_rcc.o(i.RCC_GetClocksFreq) refers to aeabi_sdiv.o(.text) for __aeabi_uidivmod
    stm32f0xx_rcc.o(i.RCC_GetClocksFreq) refers to stm32f0xx_rcc.o(.data) for APBAHBPrescTable
    stm32f0xx_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f0xx_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f0xx_tim.o(i.TIM_DeInit) refers to stm32f0xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f0xx_tim.o(i.TIM_DeInit) refers to stm32f0xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f0xx_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f0xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f0xx_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f0xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f0xx_tim.o(i.TIM_ICInit) refers to stm32f0xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f0xx_tim.o(i.TIM_ICInit) refers to stm32f0xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f0xx_tim.o(i.TIM_ICInit) refers to stm32f0xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f0xx_tim.o(i.TIM_ICInit) refers to stm32f0xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f0xx_tim.o(i.TIM_ICInit) refers to stm32f0xx_tim.o(i.TI3_Config) for TI3_Config
    stm32f0xx_tim.o(i.TIM_ICInit) refers to stm32f0xx_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f0xx_tim.o(i.TIM_ICInit) refers to stm32f0xx_tim.o(i.TI4_Config) for TI4_Config
    stm32f0xx_tim.o(i.TIM_ICInit) refers to stm32f0xx_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f0xx_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f0xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f0xx_tim.o(i.TIM_PWMIConfig) refers to stm32f0xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f0xx_tim.o(i.TIM_PWMIConfig) refers to stm32f0xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f0xx_tim.o(i.TIM_PWMIConfig) refers to stm32f0xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f0xx_tim.o(i.TIM_PWMIConfig) refers to stm32f0xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f0xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f0xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f0xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f0xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f0xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f0xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f0xx_usart.o(i.USART_DeInit) refers to stm32f0xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f0xx_usart.o(i.USART_DeInit) refers to stm32f0xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f0xx_usart.o(i.USART_Init) refers to stm32f0xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f0xx_usart.o(i.USART_Init) refers to aeabi_sdiv.o(.text) for __aeabi_uidivmod
    main.o(i.main) refers to hardware.o(i.hardware_initializes) for hardware_initializes
    main.o(i.main) refers to software.o(i.software_initializes) for software_initializes
    main.o(i.main) refers to main.o(i.send_sensor_queries) for send_sensor_queries
    main.o(i.main) refers to main.o(i.prepare_can_data) for prepare_can_data
    main.o(i.main) refers to can.o(i.CAN_SendData) for CAN_SendData
    main.o(i.main) refers to software.o(.data) for timer2_management
    main.o(i.main) refers to software.o(.bss) for data_management
    main.o(i.prepare_can_data) refers to software.o(.bss) for data_management
    main.o(i.send_sensor_queries) refers to usart2.o(i.usart2_send_data) for usart2_send_data
    main.o(i.send_sensor_queries) refers to usart1.o(i.usart1_send_data) for usart1_send_data
    main.o(i.send_sensor_queries) refers to usart4.o(i.usart4_send_data) for usart4_send_data
    main.o(i.send_sensor_queries) refers to usart5.o(i.usart5_send_data) for usart5_send_data
    main.o(i.send_sensor_queries) refers to usart6.o(i.usart6_send_data) for usart6_send_data
    main.o(i.send_sensor_queries) refers to usart8.o(i.usart8_send_data) for usart8_send_data
    main.o(i.send_sensor_queries) refers to main.o(.data) for co_query_cmd
    hardware.o(i.LED_turn) refers to stm32f0xx_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    hardware.o(i.LED_turn) refers to stm32f0xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    hardware.o(i.LED_turn) refers to stm32f0xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    hardware.o(i.hardware_initializes) refers to delay.o(i.delay_init) for delay_init
    hardware.o(i.hardware_initializes) refers to usart1.o(i.usart1_initializes) for usart1_initializes
    hardware.o(i.hardware_initializes) refers to usart2.o(i.usart2_initializes) for usart2_initializes
    hardware.o(i.hardware_initializes) refers to usart3.o(i.usart3_initializes) for usart3_initializes
    hardware.o(i.hardware_initializes) refers to usart4.o(i.usart4_initializes) for usart4_initializes
    hardware.o(i.hardware_initializes) refers to usart5.o(i.usart5_initializes) for usart5_initializes
    hardware.o(i.hardware_initializes) refers to usart6.o(i.usart6_initializes) for usart6_initializes
    hardware.o(i.hardware_initializes) refers to usart7.o(i.usart7_initializes) for usart7_initializes
    hardware.o(i.hardware_initializes) refers to usart8.o(i.usart8_initializes) for usart8_initializes
    hardware.o(i.hardware_initializes) refers to can.o(i.can_initializes) for can_initializes
    hardware.o(i.hardware_initializes) refers to timer.o(i.timer_initializes) for timer_initializes
    delay.o(i.delay_init) refers to stm32f0xx_misc.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    can.o(i.CAN1_Receive) refers to stm32f0xx_can.o(i.CAN_MessagePending) for CAN_MessagePending
    can.o(i.CAN1_Receive) refers to stm32f0xx_can.o(i.CAN_Receive) for CAN_Receive
    can.o(i.CAN1_Receive) refers to can.o(.data) for j
    can.o(i.CAN1_Receive) refers to can.o(.bss) for buf1
    can.o(i.CAN_SendData) refers to rt_memcpy.o(.text) for __aeabi_memcpy
    can.o(i.CAN_SendData) refers to stm32f0xx_can.o(i.CAN_Transmit) for CAN_Transmit
    can.o(i.CAN_SendData) refers to stm32f0xx_can.o(i.CAN_TransmitStatus) for CAN_TransmitStatus
    can.o(i.CEC_CAN_IRQHandler) refers to stm32f0xx_can.o(i.CAN_GetITStatus) for CAN_GetITStatus
    can.o(i.CEC_CAN_IRQHandler) refers to stm32f0xx_can.o(i.CAN_ClearITPendingBit) for CAN_ClearITPendingBit
    can.o(i.CEC_CAN_IRQHandler) refers to can.o(i.CAN1_Receive) for CAN1_Receive
    can.o(i.CEC_CAN_IRQHandler) refers to can.o(.data) for j
    can.o(i.can_initializes) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    can.o(i.can_initializes) refers to stm32f0xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    can.o(i.can_initializes) refers to stm32f0xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    can.o(i.can_initializes) refers to stm32f0xx_gpio.o(i.GPIO_Init) for GPIO_Init
    can.o(i.can_initializes) refers to stm32f0xx_misc.o(i.NVIC_Init) for NVIC_Init
    can.o(i.can_initializes) refers to stm32f0xx_can.o(i.CAN_DeInit) for CAN_DeInit
    can.o(i.can_initializes) refers to stm32f0xx_can.o(i.CAN_StructInit) for CAN_StructInit
    can.o(i.can_initializes) refers to stm32f0xx_can.o(i.CAN_Init) for CAN_Init
    can.o(i.can_initializes) refers to stm32f0xx_can.o(i.CAN_FilterInit) for CAN_FilterInit
    can.o(i.can_initializes) refers to stm32f0xx_can.o(i.CAN_ClearFlag) for CAN_ClearFlag
    timer.o(i.TIM2_IRQHandler) refers to stm32f0xx_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    timer.o(i.TIM2_IRQHandler) refers to stm32f0xx_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    timer.o(i.TIM2_IRQHandler) refers to software.o(.data) for timer2_management
    timer.o(i.timer_initializes) refers to stm32f0xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    timer.o(i.timer_initializes) refers to stm32f0xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer.o(i.timer_initializes) refers to stm32f0xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    timer.o(i.timer_initializes) refers to stm32f0xx_misc.o(i.NVIC_Init) for NVIC_Init
    timer.o(i.timer_initializes) refers to stm32f0xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    usart1.o(i.USART1_IRQHandler) refers to stm32f0xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart1.o(i.USART1_IRQHandler) refers to stm32f0xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    usart1.o(i.USART1_IRQHandler) refers to usart1.o(i.DMA_Enable) for DMA_Enable
    usart1.o(i.USART1_IRQHandler) refers to stm32f0xx_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    usart1.o(i.USART1_IRQHandler) refers to rt_memclr.o(.text) for __aeabi_memclr
    usart1.o(i.USART1_IRQHandler) refers to software.o(.bss) for usart1_management
    usart1.o(i.usart1_initializes) refers to stm32f0xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart1.o(i.usart1_initializes) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    usart1.o(i.usart1_initializes) refers to stm32f0xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart1.o(i.usart1_initializes) refers to stm32f0xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart1.o(i.usart1_initializes) refers to stm32f0xx_usart.o(i.USART_OverSampling8Cmd) for USART_OverSampling8Cmd
    usart1.o(i.usart1_initializes) refers to stm32f0xx_usart.o(i.USART_Init) for USART_Init
    usart1.o(i.usart1_initializes) refers to stm32f0xx_misc.o(i.NVIC_Init) for NVIC_Init
    usart1.o(i.usart1_initializes) refers to stm32f0xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart1.o(i.usart1_initializes) refers to stm32f0xx_usart.o(i.USART_DMACmd) for USART_DMACmd
    usart1.o(i.usart1_initializes) refers to stm32f0xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart1.o(i.usart1_initializes) refers to stm32f0xx_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    usart1.o(i.usart1_initializes) refers to usart1.o(i.usart_DMA_Config_TX) for usart_DMA_Config_TX
    usart1.o(i.usart1_initializes) refers to usart1.o(i.usart_DMA_Config_RX) for usart_DMA_Config_RX
    usart1.o(i.usart1_initializes) refers to software.o(.bss) for usart1_management
    usart1.o(i.usart1_send_data) refers to stm32f0xx_dma.o(i.DMA_ClearFlag) for DMA_ClearFlag
    usart1.o(i.usart1_send_data) refers to stm32f0xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    usart1.o(i.usart1_send_data) refers to rt_memcpy.o(.text) for __aeabi_memcpy
    usart1.o(i.usart1_send_data) refers to usart1.o(i.DMA_Enable) for DMA_Enable
    usart1.o(i.usart1_send_data) refers to software.o(.bss) for usart1_management
    usart1.o(i.usart_DMA_Config_RX) refers to stm32f0xx_dma.o(i.DMA_DeInit) for DMA_DeInit
    usart1.o(i.usart_DMA_Config_RX) refers to stm32f0xx_dma.o(i.DMA_Init) for DMA_Init
    usart1.o(i.usart_DMA_Config_RX) refers to stm32f0xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    usart1.o(i.usart_DMA_Config_TX) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    usart1.o(i.usart_DMA_Config_TX) refers to stm32f0xx_dma.o(i.DMA_Init) for DMA_Init
    usart2.o(i.USART2_IRQHandler) refers to stm32f0xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart2.o(i.USART2_IRQHandler) refers to stm32f0xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    usart2.o(i.USART2_IRQHandler) refers to usart1.o(i.DMA_Enable) for DMA_Enable
    usart2.o(i.USART2_IRQHandler) refers to stm32f0xx_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    usart2.o(i.USART2_IRQHandler) refers to aeabi_sdiv.o(.text) for __aeabi_idivmod
    usart2.o(i.USART2_IRQHandler) refers to rt_memclr.o(.text) for __aeabi_memclr
    usart2.o(i.USART2_IRQHandler) refers to software.o(.bss) for usart2_management
    usart2.o(i.usart2_initializes) refers to stm32f0xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    usart2.o(i.usart2_initializes) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    usart2.o(i.usart2_initializes) refers to stm32f0xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart2.o(i.usart2_initializes) refers to stm32f0xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart2.o(i.usart2_initializes) refers to stm32f0xx_usart.o(i.USART_OverSampling8Cmd) for USART_OverSampling8Cmd
    usart2.o(i.usart2_initializes) refers to stm32f0xx_usart.o(i.USART_Init) for USART_Init
    usart2.o(i.usart2_initializes) refers to stm32f0xx_misc.o(i.NVIC_Init) for NVIC_Init
    usart2.o(i.usart2_initializes) refers to stm32f0xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart2.o(i.usart2_initializes) refers to stm32f0xx_usart.o(i.USART_DMACmd) for USART_DMACmd
    usart2.o(i.usart2_initializes) refers to stm32f0xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart2.o(i.usart2_initializes) refers to stm32f0xx_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    usart2.o(i.usart2_initializes) refers to usart1.o(i.usart_DMA_Config_TX) for usart_DMA_Config_TX
    usart2.o(i.usart2_initializes) refers to usart1.o(i.usart_DMA_Config_RX) for usart_DMA_Config_RX
    usart2.o(i.usart2_initializes) refers to software.o(.bss) for usart2_management
    usart2.o(i.usart2_send_data) refers to stm32f0xx_dma.o(i.DMA_ClearFlag) for DMA_ClearFlag
    usart2.o(i.usart2_send_data) refers to stm32f0xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    usart2.o(i.usart2_send_data) refers to rt_memcpy.o(.text) for __aeabi_memcpy
    usart2.o(i.usart2_send_data) refers to usart1.o(i.DMA_Enable) for DMA_Enable
    usart2.o(i.usart2_send_data) refers to software.o(.bss) for usart2_management
    usart3.o(i.usart3_initializes) refers to stm32f0xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    usart3.o(i.usart3_initializes) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    usart3.o(i.usart3_initializes) refers to stm32f0xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart3.o(i.usart3_initializes) refers to stm32f0xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart3.o(i.usart3_initializes) refers to stm32f0xx_usart.o(i.USART_OverSampling8Cmd) for USART_OverSampling8Cmd
    usart3.o(i.usart3_initializes) refers to stm32f0xx_usart.o(i.USART_Init) for USART_Init
    usart3.o(i.usart3_initializes) refers to stm32f0xx_misc.o(i.NVIC_Init) for NVIC_Init
    usart3.o(i.usart3_initializes) refers to stm32f0xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart3.o(i.usart3_initializes) refers to stm32f0xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart3.o(i.usart3_initializes) refers to stm32f0xx_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    usart3.o(i.usart3_send_data) refers to stm32f0xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart3.o(i.usart3_send_data) refers to stm32f0xx_usart.o(i.USART_SendData) for USART_SendData
    usart4.o(i.usart4_initializes) refers to stm32f0xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    usart4.o(i.usart4_initializes) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    usart4.o(i.usart4_initializes) refers to stm32f0xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart4.o(i.usart4_initializes) refers to stm32f0xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart4.o(i.usart4_initializes) refers to stm32f0xx_usart.o(i.USART_OverSampling8Cmd) for USART_OverSampling8Cmd
    usart4.o(i.usart4_initializes) refers to stm32f0xx_usart.o(i.USART_Init) for USART_Init
    usart4.o(i.usart4_initializes) refers to stm32f0xx_misc.o(i.NVIC_Init) for NVIC_Init
    usart4.o(i.usart4_initializes) refers to stm32f0xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart4.o(i.usart4_initializes) refers to stm32f0xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart4.o(i.usart4_initializes) refers to stm32f0xx_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    usart4.o(i.usart4_send_data) refers to stm32f0xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart4.o(i.usart4_send_data) refers to stm32f0xx_usart.o(i.USART_SendData) for USART_SendData
    usart5.o(i.usart5_initializes) refers to stm32f0xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    usart5.o(i.usart5_initializes) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    usart5.o(i.usart5_initializes) refers to stm32f0xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart5.o(i.usart5_initializes) refers to stm32f0xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart5.o(i.usart5_initializes) refers to stm32f0xx_usart.o(i.USART_OverSampling8Cmd) for USART_OverSampling8Cmd
    usart5.o(i.usart5_initializes) refers to stm32f0xx_usart.o(i.USART_Init) for USART_Init
    usart5.o(i.usart5_initializes) refers to stm32f0xx_misc.o(i.NVIC_Init) for NVIC_Init
    usart5.o(i.usart5_initializes) refers to stm32f0xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart5.o(i.usart5_initializes) refers to stm32f0xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart5.o(i.usart5_initializes) refers to stm32f0xx_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    usart5.o(i.usart5_send_data) refers to stm32f0xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart5.o(i.usart5_send_data) refers to stm32f0xx_usart.o(i.USART_SendData) for USART_SendData
    usart6.o(i.usart6_initializes) refers to stm32f0xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart6.o(i.usart6_initializes) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    usart6.o(i.usart6_initializes) refers to stm32f0xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart6.o(i.usart6_initializes) refers to stm32f0xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart6.o(i.usart6_initializes) refers to stm32f0xx_usart.o(i.USART_OverSampling8Cmd) for USART_OverSampling8Cmd
    usart6.o(i.usart6_initializes) refers to stm32f0xx_usart.o(i.USART_Init) for USART_Init
    usart6.o(i.usart6_initializes) refers to stm32f0xx_misc.o(i.NVIC_Init) for NVIC_Init
    usart6.o(i.usart6_initializes) refers to stm32f0xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart6.o(i.usart6_initializes) refers to stm32f0xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart6.o(i.usart6_initializes) refers to stm32f0xx_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    usart6.o(i.usart6_send_data) refers to stm32f0xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart6.o(i.usart6_send_data) refers to stm32f0xx_usart.o(i.USART_SendData) for USART_SendData
    usart7.o(i.usart7_initializes) refers to stm32f0xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart7.o(i.usart7_initializes) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    usart7.o(i.usart7_initializes) refers to stm32f0xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart7.o(i.usart7_initializes) refers to stm32f0xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart7.o(i.usart7_initializes) refers to stm32f0xx_usart.o(i.USART_OverSampling8Cmd) for USART_OverSampling8Cmd
    usart7.o(i.usart7_initializes) refers to stm32f0xx_usart.o(i.USART_Init) for USART_Init
    usart7.o(i.usart7_initializes) refers to stm32f0xx_misc.o(i.NVIC_Init) for NVIC_Init
    usart7.o(i.usart7_initializes) refers to stm32f0xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart7.o(i.usart7_initializes) refers to stm32f0xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart7.o(i.usart7_initializes) refers to stm32f0xx_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    usart7.o(i.usart7_send_data) refers to stm32f0xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart7.o(i.usart7_send_data) refers to stm32f0xx_usart.o(i.USART_SendData) for USART_SendData
    usart8.o(i.usart8_initializes) refers to stm32f0xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart8.o(i.usart8_initializes) refers to stm32f0xx_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    usart8.o(i.usart8_initializes) refers to stm32f0xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart8.o(i.usart8_initializes) refers to stm32f0xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart8.o(i.usart8_initializes) refers to stm32f0xx_usart.o(i.USART_OverSampling8Cmd) for USART_OverSampling8Cmd
    usart8.o(i.usart8_initializes) refers to stm32f0xx_usart.o(i.USART_Init) for USART_Init
    usart8.o(i.usart8_initializes) refers to stm32f0xx_misc.o(i.NVIC_Init) for NVIC_Init
    usart8.o(i.usart8_initializes) refers to stm32f0xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart8.o(i.usart8_initializes) refers to stm32f0xx_usart.o(i.USART_DMACmd) for USART_DMACmd
    usart8.o(i.usart8_initializes) refers to stm32f0xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart8.o(i.usart8_initializes) refers to stm32f0xx_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    usart8.o(i.usart8_send_data) refers to stm32f0xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart8.o(i.usart8_send_data) refers to stm32f0xx_usart.o(i.USART_SendData) for USART_SendData
    software.o(i.software_initializes) refers to rt_memclr.o(.text) for __aeabi_memclr4
    software.o(i.software_initializes) refers to software.o(.bss) for usart1_management
    software.o(i.software_initializes) refers to software.o(.data) for timer2_management
    crc16.o(i.Crc16) refers to crc16.o(.constdata) for u16CrcTalbeAbs
    rt_memcpy.o(.text) refers to rt_memcpy.o(.emb_text) for __aeabi_memcpy4
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_idiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f091.o(.text) for __user_initial_stackheap
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to tempstk.o(.text) for __temporary_stack_top
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to __main.o(!!!main) for __main
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing stm32f0xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_it.o(.revsh_text), (4 bytes).
    Removing system_stm32f0xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f0xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f0xx.o(i.SystemCoreClockUpdate), (176 bytes).
    Removing system_stm32f0xx.o(.data), (20 bytes).
    Removing stm32f0xx_can.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_can.o(.revsh_text), (4 bytes).
    Removing stm32f0xx_can.o(i.CAN_CancelTransmit), (54 bytes).
    Removing stm32f0xx_can.o(i.CAN_DBGFreeze), (28 bytes).
    Removing stm32f0xx_can.o(i.CAN_FIFORelease), (24 bytes).
    Removing stm32f0xx_can.o(i.CAN_GetFlagStatus), (146 bytes).
    Removing stm32f0xx_can.o(i.CAN_GetLSBTransmitErrorCounter), (16 bytes).
    Removing stm32f0xx_can.o(i.CAN_GetLastErrorCode), (14 bytes).
    Removing stm32f0xx_can.o(i.CAN_GetReceiveErrorCounter), (10 bytes).
    Removing stm32f0xx_can.o(i.CAN_ITConfig), (20 bytes).
    Removing stm32f0xx_can.o(i.CAN_OperatingModeRequest), (172 bytes).
    Removing stm32f0xx_can.o(i.CAN_SlaveStartBank), (56 bytes).
    Removing stm32f0xx_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f0xx_can.o(i.CAN_TTComModeCmd), (128 bytes).
    Removing stm32f0xx_can.o(i.CAN_WakeUp), (52 bytes).
    Removing stm32f0xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f0xx_dma.o(i.DMA_ClearITPendingBit), (32 bytes).
    Removing stm32f0xx_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f0xx_dma.o(i.DMA_GetFlagStatus), (52 bytes).
    Removing stm32f0xx_dma.o(i.DMA_GetITStatus), (52 bytes).
    Removing stm32f0xx_dma.o(i.DMA_ITConfig), (20 bytes).
    Removing stm32f0xx_dma.o(i.DMA_RemapConfig), (52 bytes).
    Removing stm32f0xx_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f0xx_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f0xx_exti.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_exti.o(.revsh_text), (4 bytes).
    Removing stm32f0xx_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f0xx_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f0xx_exti.o(i.EXTI_DeInit), (32 bytes).
    Removing stm32f0xx_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f0xx_exti.o(i.EXTI_GetFlagStatus), (28 bytes).
    Removing stm32f0xx_exti.o(i.EXTI_GetITStatus), (28 bytes).
    Removing stm32f0xx_exti.o(i.EXTI_Init), (136 bytes).
    Removing stm32f0xx_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f0xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_DeInit), (176 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_PinLockConfig), (34 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_ReadInputData), (6 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_ReadInputDataBit), (20 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_ReadOutputData), (6 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_ReadOutputDataBit), (20 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_ResetBits), (4 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_SetBits), (4 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_StructInit), (24 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f0xx_gpio.o(i.GPIO_WriteBit), (12 bytes).
    Removing stm32f0xx_misc.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_misc.o(.revsh_text), (4 bytes).
    Removing stm32f0xx_misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing stm32f0xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_ADCCLKConfig), (56 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_AHBPeriphResetCmd), (32 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_AdjustHSI14CalibrationValue), (32 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_AdjustHSICalibrationValue), (32 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_BackupResetCmd), (40 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_CECCLKConfig), (28 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_ClockSecuritySystemCmd), (40 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_DeInit), (120 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_GetFlagStatus), (72 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_GetITStatus), (28 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_HCLKConfig), (32 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_HSEConfig), (16 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_HSI14ADCRequestCmd), (36 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_HSI14Cmd), (36 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_HSI48Cmd), (40 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_HSICmd), (36 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_I2CCLKConfig), (28 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_LSEConfig), (40 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_LSEDriveConfig), (28 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_LSICmd), (36 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_MCOConfig), (28 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_PCLKConfig), (36 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_PLLCmd), (40 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_PLLConfig), (36 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_PREDIV1Config), (28 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_RTCCLKCmd), (40 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_SYSCLKConfig), (28 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_USARTCLKConfig), (72 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_USBCLKConfig), (28 bytes).
    Removing stm32f0xx_rcc.o(i.RCC_WaitForHSEStartUp), (60 bytes).
    Removing stm32f0xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f0xx_tim.o(i.TI1_Config), (56 bytes).
    Removing stm32f0xx_tim.o(i.TI2_Config), (76 bytes).
    Removing stm32f0xx_tim.o(i.TI3_Config), (72 bytes).
    Removing stm32f0xx_tim.o(i.TI4_Config), (80 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ARRPreloadConfig), (28 bytes).
    Removing stm32f0xx_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f0xx_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f0xx_tim.o(i.TIM_CCPreloadControl), (28 bytes).
    Removing stm32f0xx_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f0xx_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ClearOC1Ref), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ClearOC2Ref), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ClearOC3Ref), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ClearOC4Ref), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_CounterModeConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_CtrlPWMOutputs), (34 bytes).
    Removing stm32f0xx_tim.o(i.TIM_DMACmd), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_DMAConfig), (12 bytes).
    Removing stm32f0xx_tim.o(i.TIM_DeInit), (260 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ETRClockMode1Config), (52 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ETRClockMode2Config), (34 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ETRConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_EncoderInterfaceConfig), (68 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ForcedOC1Config), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ForcedOC2Config), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ForcedOC3Config), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ForcedOC4Config), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f0xx_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_GetCapture4), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_GetFlagStatus), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ICInit), (112 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f0xx_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_InternalClockConfig), (16 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC1FastConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC1Init), (148 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC1NPolarityConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC1PolarityConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC1PreloadConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC2FastConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC2Init), (172 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC2NPolarityConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC2PolarityConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC2PreloadConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC3FastConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC3Init), (152 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC3NPolarityConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC3PolarityConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC3PreloadConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC4FastConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC4Init), (112 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC4PolarityConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OC4PreloadConfig), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f0xx_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_RemapConfig), (6 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectCCDMA), (28 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectCOM), (28 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectHallSensor), (28 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectInputTrigger), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectMasterSlaveMode), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectOCREFClear), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectOCxM), (82 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectOnePulseMode), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectOutputTrigger), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SelectSlaveMode), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetClockDivision), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetCompare4), (4 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetIC1Prescaler), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetIC2Prescaler), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetIC3Prescaler), (20 bytes).
    Removing stm32f0xx_tim.o(i.TIM_SetIC4Prescaler), (24 bytes).
    Removing stm32f0xx_tim.o(i.TIM_TIxExternalClockConfig), (58 bytes).
    Removing stm32f0xx_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f0xx_tim.o(i.TIM_UpdateDisableConfig), (28 bytes).
    Removing stm32f0xx_tim.o(i.TIM_UpdateRequestConfig), (28 bytes).
    Removing stm32f0xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f0xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f0xx_usart.o(i.USART_AddressDetectionConfig), (16 bytes).
    Removing stm32f0xx_usart.o(i.USART_AutoBaudRateCmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_AutoBaudRateConfig), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_ClockInit), (36 bytes).
    Removing stm32f0xx_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f0xx_usart.o(i.USART_DECmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_DEPolarityConfig), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_DMAReceptionErrorConfig), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_DataInvCmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_DeInit), (240 bytes).
    Removing stm32f0xx_usart.o(i.USART_DirectionModeCmd), (20 bytes).
    Removing stm32f0xx_usart.o(i.USART_GetITStatus), (78 bytes).
    Removing stm32f0xx_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f0xx_usart.o(i.USART_InvPinCmd), (20 bytes).
    Removing stm32f0xx_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f0xx_usart.o(i.USART_IrDAConfig), (16 bytes).
    Removing stm32f0xx_usart.o(i.USART_LINBreakDetectLengthConfig), (16 bytes).
    Removing stm32f0xx_usart.o(i.USART_LINCmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_MSBFirstCmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_MuteModeCmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_MuteModeWakeUpConfig), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_OneBitMethodCmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_OverrunDetectionConfig), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_ReceiverTimeOutCmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_RequestCmd), (20 bytes).
    Removing stm32f0xx_usart.o(i.USART_STOPModeCmd), (24 bytes).
    Removing stm32f0xx_usart.o(i.USART_SWAPPinCmd), (28 bytes).
    Removing stm32f0xx_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_SetAutoRetryCount), (20 bytes).
    Removing stm32f0xx_usart.o(i.USART_SetBlockLength), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_SetDEAssertionTime), (20 bytes).
    Removing stm32f0xx_usart.o(i.USART_SetDEDeassertionTime), (20 bytes).
    Removing stm32f0xx_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f0xx_usart.o(i.USART_SetPrescaler), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_SetReceiverTimeOut), (16 bytes).
    Removing stm32f0xx_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f0xx_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f0xx_usart.o(i.USART_StopModeWakeUpSourceConfig), (18 bytes).
    Removing stm32f0xx_usart.o(i.USART_StructInit), (24 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing hardware.o(.rev16_text), (4 bytes).
    Removing hardware.o(.revsh_text), (4 bytes).
    Removing hardware.o(i.LED_turn), (44 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(i.delay_ms), (60 bytes).
    Removing delay.o(i.delay_us), (52 bytes).
    Removing can.o(.rev16_text), (4 bytes).
    Removing can.o(.revsh_text), (4 bytes).
    Removing timer.o(.rev16_text), (4 bytes).
    Removing timer.o(.revsh_text), (4 bytes).
    Removing usart1.o(.rev16_text), (4 bytes).
    Removing usart1.o(.revsh_text), (4 bytes).
    Removing usart2.o(.rev16_text), (4 bytes).
    Removing usart2.o(.revsh_text), (4 bytes).
    Removing usart3.o(.rev16_text), (4 bytes).
    Removing usart3.o(.revsh_text), (4 bytes).
    Removing usart3.o(i.usart3_send_data), (60 bytes).
    Removing usart4.o(.rev16_text), (4 bytes).
    Removing usart4.o(.revsh_text), (4 bytes).
    Removing usart5.o(.rev16_text), (4 bytes).
    Removing usart5.o(.revsh_text), (4 bytes).
    Removing usart6.o(.rev16_text), (4 bytes).
    Removing usart6.o(.revsh_text), (4 bytes).
    Removing usart7.o(.rev16_text), (4 bytes).
    Removing usart7.o(.revsh_text), (4 bytes).
    Removing usart7.o(i.usart7_send_data), (60 bytes).
    Removing usart8.o(.rev16_text), (4 bytes).
    Removing usart8.o(.revsh_text), (4 bytes).
    Removing software.o(.rev16_text), (4 bytes).
    Removing software.o(.revsh_text), (4 bytes).
    Removing crc16.o(i.Crc16), (72 bytes).
    Removing crc16.o(.constdata), (32 bytes).

261 unused section(s) (total 7894 bytes) removed from the image.
