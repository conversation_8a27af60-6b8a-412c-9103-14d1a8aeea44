#ifndef __Software_H
#define __Software_H

#include "Hardware.h"

// 缓冲区大小定义
#define USART_SEND_BUFFER_SIZE      20
#define USART_RECEIVE_BUFFER_SIZE   20

// USART管理结构体定义
typedef struct
{
	uint8_t sendBuffer[USART_SEND_BUFFER_SIZE];
	uint8_t receiveBuffer[USART_RECEIVE_BUFFER_SIZE];
}usart_management_t;

// 外部声明各USART管理结构体
extern usart_management_t usart1_management;  // 膨胀力传感器3
extern usart_management_t usart2_management;  // CO传感器
extern usart_management_t usart3_management;  // H2传感器
extern usart_management_t usart4_management;  // 膨胀力传感器1
extern usart_management_t usart5_management;  // 膨胀力传感器2
extern usart_management_t usart6_management;  // 膨胀力传感器4
extern usart_management_t usart7_management;  // O2传感器
extern usart_management_t usart8_management;  // CO2传感器

// 数据管理结构体
typedef struct
{
	uint16_t co_concentration;      // CO浓度
	uint16_t h2_concentration;      // H2浓度
	uint16_t o2_concentration;      // O2浓度
	uint16_t co2_concentration;     // CO2浓度
	uint32_t expansion_force1;      // 膨胀力1
	uint32_t expansion_force2;      // 膨胀力2
	uint32_t expansion_force3;      // 膨胀力3
	uint32_t expansion_force4;      // 膨胀力4
	uint8_t can_data[32];          // CAN发送数据缓冲区
}data_management_t;

extern data_management_t data_management;

// 定时器管理结构体
typedef struct
{
	uint8_t count;
	uint8_t flag_100ms;
	uint8_t flag_500ms;
	uint8_t flag_900ms;
	uint8_t flag_1s;
}timer2_management_t;

extern timer2_management_t timer2_management;

void software_initializes(void);

#endif

