#include "Usart5.h"

/******************************************************************
 * @brief  初始化串口5，用于膨胀力传感器2通信
 *         TX连接PB3、RX连接PB4
 * @input  无
 * @return 无
******************************************************************/
void usart5_initializes(void)
{
    USART_InitTypeDef USART_InitStructure;
    GPIO_InitTypeDef  GPIO_InitStructure;
    NVIC_InitTypeDef  NVIC_InitStructure;
    
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART5, ENABLE);
    RCC_AHBPeriphClockCmd(RCC_AHBPeriph_GPIOB, ENABLE);

    // 配置GPIO复用功能
    GPIO_PinAFConfig(GPIOB, GPIO_PinSource3, GPIO_AF_4);  // TX
    GPIO_PinAFConfig(GPIOB, GPIO_PinSource4, GPIO_AF_4);  // RX

    // 配置GPIO引脚
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_3 | GPIO_Pin_4;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
    GPIO_Init(GPIOB, &GPIO_InitStructure);

    // 配置USART参数
    USART_OverSampling8Cmd(USART5, ENABLE);
    USART_InitStructure.USART_BaudRate = 9600;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Rx | USART_Mode_Tx;
    USART_Init(USART5, &USART_InitStructure);
    
    // 配置中断 - USART3-8共享中断
    NVIC_InitStructure.NVIC_IRQChannel = USART3_8_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    // 使能接收中断
    USART_ITConfig(USART5, USART_IT_RXNE, ENABLE);
    USART_Cmd(USART5, ENABLE);
    USART_ClearFlag(USART5, USART_FLAG_TC);
}

/******************************************************************
 * @brief  通过DMA发送数据
 * @input  data：数据
 *         len：数据长度
 * @return 无
******************************************************************/
void usart5_send_data(uint8_t *data, uint8_t len)
{
    uint8_t i;
    for(i = 0; i < len; i++)
    {
        while(USART_GetFlagStatus(USART5, USART_FLAG_TXE) == RESET);
        USART_SendData(USART5, data[i]);
    }
    while(USART_GetFlagStatus(USART5, USART_FLAG_TC) == RESET);
}
