/**
  ******************************************************************************
  * @file    Project/STM32F0xx_StdPeriph_Templates/stm32f0xx_it.c 
  * <AUTHOR> Application Team
  * @version V1.6.0
  * @date    13-October-2021
  * @brief   Main Interrupt Service Routines.
  *          This file provides template for all exceptions handler and 
  *          peripherals interrupt service routine.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2014 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "stm32f0xx_it.h"
#include "Hardware.h"

/** @addtogroup STM32F0xx_StdPeriph_Templates
  * @{
  */

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
/* Private functions ---------------------------------------------------------*/

/******************************************************************************/
/*            Cortex-M0 Processor Exceptions Handlers                         */
/******************************************************************************/

/**
  * @brief  This function handles NMI exception.
  * @param  None
  * @retval None
  */
void NMI_Handler(void)
{
}

/**
  * @brief  This function handles Hard Fault exception.
  * @param  None
  * @retval None
  */
void HardFault_Handler(void)
{
  /* Go to infinite loop when Hard Fault exception occurs */
  while (1)
  {
  }
}

/**
  * @brief  This function handles SVCall exception.
  * @param  None
  * @retval None
  */
void SVC_Handler(void)
{
}

/**
  * @brief  This function handles PendSVC exception.
  * @param  None
  * @retval None
  */
void PendSV_Handler(void)
{
}

/**
  * @brief  This function handles SysTick Handler.
  * @param  None
  * @retval None
  */
void SysTick_Handler(void)
{
}

/**
  * @brief  This function handles USART3-8 global interrupt.
  * @param  None
  * @retval None
  */
void USART3_8_IRQHandler(void)
{
    // USART3中断处理 - H2传感器（使用简化的接收处理）
    if (USART_GetFlagStatus(USART3, USART_FLAG_RXNE) != RESET)
    {
        static uint8_t usart3_rx_index = 0;
        uint8_t received_data = USART_ReceiveData(USART3);

        if(usart3_rx_index < USART_RECEIVE_BUFFER_SIZE)
        {
            usart3_management.receiveBuffer[usart3_rx_index++] = received_data;
        }

        // 简单的帧结束检测（H2传感器数据长度为9字节）
        if(usart3_rx_index >= 9)
        {
            // 处理H2传感器数据
            // 传感器应答：FF 86 00（高位） D1（低位） 05（气体代码） 01（小数位数） 00 00 A3
            // 气体浓度值=（气体浓度高位×256+气体浓度低位）×分辨率
            if(usart3_management.receiveBuffer[0] == 0xFF && usart3_management.receiveBuffer[1] == 0x86)
            {
                uint16_t h2_raw = (uint16_t)(usart3_management.receiveBuffer[2] << 8) | usart3_management.receiveBuffer[3];
                uint8_t decimal_places = usart3_management.receiveBuffer[5];
                uint8_t i;

                // 根据小数位数计算实际浓度
                data_management.h2_concentration = h2_raw;
                for(i = 0; i < decimal_places; i++)
                {
                    data_management.h2_concentration /= 10;
                }

                // LED闪烁指示
                led_flash(LED3);
            }

            memset(usart3_management.receiveBuffer, 0, sizeof(usart3_management.receiveBuffer));
            usart3_rx_index = 0;
        }
    }

    // USART4中断处理 - 膨胀力传感器1（使用简化的接收处理）
    if (USART_GetFlagStatus(USART4, USART_FLAG_RXNE) != RESET)
    {
        static uint8_t usart4_rx_index = 0;
        uint8_t received_data = USART_ReceiveData(USART4);

        if(usart4_rx_index < USART_RECEIVE_BUFFER_SIZE)
        {
            usart4_management.receiveBuffer[usart4_rx_index++] = received_data;
        }

        // 膨胀力传感器响应数据长度为9字节
        if(usart4_rx_index >= 9)
        {
            // 处理膨胀力传感器1数据（与传感器3相同）
            if(usart4_management.receiveBuffer[0] == 0x01 && usart4_management.receiveBuffer[1] == 0x03 &&
               usart4_management.receiveBuffer[2] == 0x04)
            {
                data_management.expansion_force1 = (uint32_t)(usart4_management.receiveBuffer[3] << 24) |
                                                  (uint32_t)(usart4_management.receiveBuffer[4] << 16) |
                                                  (uint32_t)(usart4_management.receiveBuffer[5] << 8) |
                                                  (uint32_t)(usart4_management.receiveBuffer[6]);

                led_flash(LED4);
            }

            memset(usart4_management.receiveBuffer, 0, sizeof(usart4_management.receiveBuffer));
            usart4_rx_index = 0;
        }
    }

    // USART5中断处理 - 膨胀力传感器2（使用简化的接收处理）
    if (USART_GetFlagStatus(USART5, USART_FLAG_RXNE) != RESET)
    {
        static uint8_t usart5_rx_index = 0;
        uint8_t received_data = USART_ReceiveData(USART5);

        if(usart5_rx_index < USART_RECEIVE_BUFFER_SIZE)
        {
            usart5_management.receiveBuffer[usart5_rx_index++] = received_data;
        }

        // 膨胀力传感器响应数据长度为9字节
        if(usart5_rx_index >= 9)
        {
            // 处理膨胀力传感器2数据（与传感器3相同）
            if(usart5_management.receiveBuffer[0] == 0x01 && usart5_management.receiveBuffer[1] == 0x03 &&
               usart5_management.receiveBuffer[2] == 0x04)
            {
                data_management.expansion_force2 = (uint32_t)(usart5_management.receiveBuffer[3] << 24) |
                                                  (uint32_t)(usart5_management.receiveBuffer[4] << 16) |
                                                  (uint32_t)(usart5_management.receiveBuffer[5] << 8) |
                                                  (uint32_t)(usart5_management.receiveBuffer[6]);

                led_flash(LED5);
            }

            memset(usart5_management.receiveBuffer, 0, sizeof(usart5_management.receiveBuffer));
            usart5_rx_index = 0;
        }
    }

    // USART6中断处理 - 膨胀力传感器4（使用简化的接收处理）
    if (USART_GetFlagStatus(USART6, USART_FLAG_RXNE) != RESET)
    {
        static uint8_t usart6_rx_index = 0;
        uint8_t received_data = USART_ReceiveData(USART6);

        if(usart6_rx_index < USART_RECEIVE_BUFFER_SIZE)
        {
            usart6_management.receiveBuffer[usart6_rx_index++] = received_data;
        }

        // 膨胀力传感器响应数据长度为9字节
        if(usart6_rx_index >= 9)
        {
            // 处理膨胀力传感器4数据（与传感器3相同）
            if(usart6_management.receiveBuffer[0] == 0x01 && usart6_management.receiveBuffer[1] == 0x03 &&
               usart6_management.receiveBuffer[2] == 0x04)
            {
                data_management.expansion_force4 = (uint32_t)(usart6_management.receiveBuffer[3] << 24) |
                                                  (uint32_t)(usart6_management.receiveBuffer[4] << 16) |
                                                  (uint32_t)(usart6_management.receiveBuffer[5] << 8) |
                                                  (uint32_t)(usart6_management.receiveBuffer[6]);

                led_flash(LED6);
            }

            memset(usart6_management.receiveBuffer, 0, sizeof(usart6_management.receiveBuffer));
            usart6_rx_index = 0;
        }
    }

    // USART7中断处理 - O2传感器（使用简化的接收处理）
    if (USART_GetFlagStatus(USART7, USART_FLAG_RXNE) != RESET)
    {
        static uint8_t usart7_rx_index = 0;
        uint8_t received_data = USART_ReceiveData(USART7);

        if(usart7_rx_index < USART_RECEIVE_BUFFER_SIZE)
        {
            usart7_management.receiveBuffer[usart7_rx_index++] = received_data;
        }

        // 简单的帧结束检测（可以根据实际协议调整）
        if(usart7_rx_index >= 9) // O2传感器数据长度
        {
            // 处理O2传感器数据（与H2传感器相同）
            if(usart7_management.receiveBuffer[0] == 0xFF && usart7_management.receiveBuffer[1] == 0x86)
            {
                uint16_t o2_raw = (uint16_t)(usart7_management.receiveBuffer[2] << 8) | usart7_management.receiveBuffer[3];
                uint8_t decimal_places = usart7_management.receiveBuffer[5];
                uint8_t i;

                data_management.o2_concentration = o2_raw;
                for(i = 0; i < decimal_places; i++)
                {
                    data_management.o2_concentration /= 10;
                }

                led_flash(LED7);
            }

            memset(usart7_management.receiveBuffer, 0, sizeof(usart7_management.receiveBuffer));
            usart7_rx_index = 0;
        }
    }

    // USART8中断处理 - CO2传感器（使用简化的接收处理）
    if (USART_GetFlagStatus(USART8, USART_FLAG_RXNE) != RESET)
    {
        static uint8_t usart8_rx_index = 0;
        uint8_t received_data = USART_ReceiveData(USART8);

        if(usart8_rx_index < USART_RECEIVE_BUFFER_SIZE)
        {
            usart8_management.receiveBuffer[usart8_rx_index++] = received_data;
        }

        // 简单的帧结束检测
        if(usart8_rx_index >= 9) // CO2传感器响应数据长度
        {
            // 处理CO2传感器数据
            // 读浓度指令：FF 03 60 01 00 02 9E 15
            // 返回数据：FF 03 04 00（高位） 00（低位） 77 B5 03 BB
            // 小数点个数为1，浓度要除以10
            if(usart8_management.receiveBuffer[0] == 0xFF && usart8_management.receiveBuffer[1] == 0x03 &&
               usart8_management.receiveBuffer[2] == 0x04)
            {
                uint16_t co2_raw = (uint16_t)(usart8_management.receiveBuffer[3] << 8) | usart8_management.receiveBuffer[4];
                data_management.co2_concentration = co2_raw / 10;  // 小数点个数为1

                led_flash(LED8);
            }

            memset(usart8_management.receiveBuffer, 0, sizeof(usart8_management.receiveBuffer));
            usart8_rx_index = 0;
        }
    }
}

/******************************************************************************/
/*                 STM32F0xx Peripherals Interrupt Handlers                   */
/*  Add here the Interrupt Handler for the used peripheral(s) (PPP), for the  */
/*  available peripheral interrupt handler's name please refer to the startup */
/*  file (startup_stm32f0xx.s).                                            */
/******************************************************************************/

/**
  * @brief  This function handles PPP interrupt request.
  * @param  None
  * @retval None
  */
/*void PPP_IRQHandler(void)
{
}*/

/**
  * @}
  */ 


